#include "WebServerManager.h"
#include <Arduino.h>

WebServerManager::WebServerManager(SignalService* signalService, TaskService* taskService,
                                   TimerService* timerService, SystemService* systemService,
                                   WSManager* wsManager)
    : m_webServer(nullptr), m_signalService(signalService), m_taskService(taskService),
      m_timerService(timerService), m_systemService(systemService), m_wsManager(wsManager),
      m_initialized(false), m_running(false) {
}

WebServerManager::~WebServerManager() {
    cleanup();
}

bool WebServerManager::initialize() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        return true;
    }
    
    // 验证依赖组件
    if (!m_signalService || !m_taskService || !m_timerService || !m_systemService) {
        Serial.println("❌ WebServerManager: Missing required dependencies");
        return false;
    }
    
    // 检查依赖组件是否已初始化
    if (!m_signalService->isInitialized() || !m_taskService->isInitialized() || 
        !m_timerService->isInitialized() || !m_systemService->isInitialized()) {
        Serial.println("❌ WebServerManager: Dependencies not initialized");
        return false;
    }
    
    // 初始化Web服务器实例
    if (!initializeWebServer()) {
        Serial.println("❌ WebServerManager: Failed to initialize web server");
        return false;
    }
    
    // 注册所有路由
    registerAllRoutes();
    
    // 设置默认处理器
    setupDefaultHandlers();
    
    m_initialized = true;
    
    Serial.println("✅ WebServerManager: Initialized successfully");
    return true;
}

bool WebServerManager::start() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        Serial.println("❌ WebServerManager: Not initialized");
        return false;
    }
    
    if (m_running) {
        return true;
    }
    
    // 启动Web服务器
    m_webServer->begin();
    m_running = true;
    
    Serial.printf("✅ WebServerManager: Server started on port %d\n", m_serverConfig.port);
    return true;
}

bool WebServerManager::stop() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_running) {
        return true;
    }
    
    // 停止Web服务器
    m_webServer->end();
    m_running = false;
    
    Serial.println("✅ WebServerManager: Server stopped");
    return true;
}

void WebServerManager::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        // 停止服务器
        stop();
        
        // 删除Web服务器实例
        if (m_webServer) {
            delete m_webServer;
            m_webServer = nullptr;
        }
        
        m_initialized = false;
        
        Serial.println("✅ WebServerManager: Cleanup completed");
    }
}

void WebServerManager::registerAllRoutes() {
    // 注册静态文件路由
    registerStaticRoutes();
    
    // 注册API路由
    registerAPIRoutes();
    
    Serial.println("✅ WebServerManager: All routes registered");
}

void WebServerManager::registerStaticRoutes() {
    // 根路径重定向到index.html
    m_webServer->on("/", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->serveStaticFile(request, "/index.html");
    });
    
    // 静态文件服务
    m_webServer->serveStatic("/", LittleFS, "/").setDefaultFile("index.html");
    
    Serial.println("✅ WebServerManager: Static routes registered");
}

void WebServerManager::registerAPIRoutes() {
    // 注册各模块API
    registerSignalAPI();
    registerTaskAPI();
    registerTimerAPI();
    registerSystemAPI();
    registerConfigAPI();
    registerOTAAPI();
    
    Serial.println("✅ WebServerManager: API routes registered");
}

void WebServerManager::registerSignalAPI() {
    // GET /api/signals - 获取所有信号
    m_webServer->on("/api/signals", HTTP_GET, [this](AsyncWebServerRequest* request) {
        logRequest(request);
        
        try {
            auto signals = m_signalService->getAllSignals();
            
            DynamicJsonDocument doc(signals.size() * 512 + 1024);
            JsonArray signalsArray = JSONConverter::signalsToJsonArray(signals, doc);
            
            JsonObject responseData = doc.createNestedObject("data");
            responseData["signals"] = signalsArray;
            responseData["count"] = signals.size();
            
            sendJSONResponse(request, 200, true, "Signals retrieved successfully", &responseData);
            
        } catch (const std::exception& e) {
            handle500(request, "Failed to retrieve signals: " + String(e.what()));
        }
    });
    
    // POST /api/signals - 创建新信号
    m_webServer->on("/api/signals", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            if (index + len != total) {
                return; // 等待所有数据接收完成
            }
            
            logRequest(request);
            
            try {
                DynamicJsonDocument doc(1024);
                if (!parseJSONBody(data, len, doc)) {
                    sendJSONResponse(request, 400, false, "Invalid JSON format");
                    return;
                }
                
                auto signalResult = JSONConverter::signalFromJson(doc.as<JsonObject>());
                if (!signalResult.isSuccess()) {
                    sendJSONResponse(request, 400, false, "Invalid signal data: " + signalResult.getErrorMessage());
                    return;
                }
                
                auto createResult = m_signalService->createSignal(signalResult.getValue());
                if (createResult.isSuccess()) {
                    DynamicJsonDocument responseDoc(1024);
                    JsonObject signalObj = JSONConverter::signalToJson(createResult.getValue(), responseDoc);
                    
                    JsonObject responseData = responseDoc.createNestedObject("data");
                    responseData["signal"] = signalObj;
                    
                    sendJSONResponse(request, 201, true, "Signal created successfully", &responseData);
                } else {
                    sendJSONResponse(request, 400, false, "Failed to create signal: " + createResult.getErrorMessage());
                }
                
            } catch (const std::exception& e) {
                handle500(request, "Internal error: " + String(e.what()));
            }
        }
    );
    
    // GET /api/signals/{id} - 获取特定信号
    m_webServer->on("^\\/api\\/signals\\/([0-9]+)$", HTTP_GET, [this](AsyncWebServerRequest* request) {
        logRequest(request);
        
        SignalID signalId = request->pathArg(0).toInt();
        if (signalId <= 0) {
            sendJSONResponse(request, 400, false, "Invalid signal ID");
            return;
        }
        
        try {
            auto result = m_signalService->getSignal(signalId);
            if (result.isSuccess()) {
                DynamicJsonDocument doc(1024);
                JsonObject signalObj = JSONConverter::signalToJson(result.getValue(), doc);
                
                JsonObject responseData = doc.createNestedObject("data");
                responseData["signal"] = signalObj;
                
                sendJSONResponse(request, 200, true, "Signal retrieved successfully", &responseData);
            } else {
                sendJSONResponse(request, 404, false, "Signal not found");
            }
            
        } catch (const std::exception& e) {
            handle500(request, "Failed to retrieve signal: " + String(e.what()));
        }
    });
    
    // POST /api/signals/{id}/send - 发送特定信号
    m_webServer->on("^\\/api\\/signals\\/([0-9]+)\\/send$", HTTP_POST, [this](AsyncWebServerRequest* request) {
        logRequest(request);
        
        SignalID signalId = request->pathArg(0).toInt();
        if (signalId <= 0) {
            sendJSONResponse(request, 400, false, "Invalid signal ID");
            return;
        }
        
        try {
            bool success = m_signalService->sendSignal(signalId);
            if (success) {
                sendJSONResponse(request, 200, true, "Signal sent successfully");
            } else {
                sendJSONResponse(request, 400, false, "Failed to send signal");
            }
            
        } catch (const std::exception& e) {
            handle500(request, "Failed to send signal: " + String(e.what()));
        }
    });
    
    // POST /api/signals/learn/start - 开始学习信号
    m_webServer->on("/api/signals/learn/start", HTTP_POST, [this](AsyncWebServerRequest* request) {
        logRequest(request);
        
        try {
            bool success = m_signalService->startLearning();
            if (success) {
                sendJSONResponse(request, 200, true, "Learning started successfully");
            } else {
                sendJSONResponse(request, 400, false, "Failed to start learning");
            }
            
        } catch (const std::exception& e) {
            handle500(request, "Failed to start learning: " + String(e.what()));
        }
    });
    
    // POST /api/signals/learn/stop - 停止学习信号
    m_webServer->on("/api/signals/learn/stop", HTTP_POST, [this](AsyncWebServerRequest* request) {
        logRequest(request);
        
        try {
            bool success = m_signalService->stopLearning();
            if (success) {
                sendJSONResponse(request, 200, true, "Learning stopped successfully");
            } else {
                sendJSONResponse(request, 400, false, "Failed to stop learning");
            }
            
        } catch (const std::exception& e) {
            handle500(request, "Failed to stop learning: " + String(e.what()));
        }
    });
    
    // GET /api/signals/learn/status - 获取学习状态
    m_webServer->on("/api/signals/learn/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        logRequest(request);
        
        try {
            DynamicJsonDocument doc(512);
            JsonObject responseData = doc.createNestedObject("data");
            responseData["status"] = m_signalService->getLearningStatus();
            responseData["progress"] = m_signalService->getLearningProgress();
            
            sendJSONResponse(request, 200, true, "Learning status retrieved", &responseData);
            
        } catch (const std::exception& e) {
            handle500(request, "Failed to get learning status: " + String(e.what()));
        }
    });
    
    Serial.println("✅ WebServerManager: Signal API registered");
}

void WebServerManager::registerTaskAPI() {
    // GET /api/tasks - 获取所有任务
    m_webServer->on("/api/tasks", HTTP_GET, [this](AsyncWebServerRequest* request) {
        logRequest(request);

        try {
            auto tasks = m_taskService->getAllTasks();

            DynamicJsonDocument doc(tasks.size() * 512 + 1024);
            JsonArray tasksArray = JSONConverter::tasksToJsonArray(tasks, doc);

            JsonObject responseData = doc.createNestedObject("data");
            responseData["tasks"] = tasksArray;
            responseData["count"] = tasks.size();

            sendJSONResponse(request, 200, true, "Tasks retrieved successfully", &responseData);

        } catch (const std::exception& e) {
            handle500(request, "Failed to retrieve tasks: " + String(e.what()));
        }
    });

    Serial.println("✅ WebServerManager: Task API registered");
}

void WebServerManager::registerTimerAPI() {
    // GET /api/timers - 获取所有定时器
    m_webServer->on("/api/timers", HTTP_GET, [this](AsyncWebServerRequest* request) {
        logRequest(request);

        try {
            auto timers = m_timerService->getAllTimers();

            DynamicJsonDocument doc(timers.size() * 512 + 1024);
            JsonArray timersArray = JSONConverter::timersToJsonArray(timers, doc);

            JsonObject responseData = doc.createNestedObject("data");
            responseData["timers"] = timersArray;
            responseData["count"] = timers.size();

            sendJSONResponse(request, 200, true, "Timers retrieved successfully", &responseData);

        } catch (const std::exception& e) {
            handle500(request, "Failed to retrieve timers: " + String(e.what()));
        }
    });

    Serial.println("✅ WebServerManager: Timer API registered");
}

void WebServerManager::registerSystemAPI() {
    // GET /api/system/status - 获取系统状态
    m_webServer->on("/api/system/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        logRequest(request);

        try {
            auto status = m_systemService->getSystemStatus();

            DynamicJsonDocument doc(1024);
            JsonObject statusObj = JSONConverter::systemStatusToJson(status, doc);

            JsonObject responseData = doc.createNestedObject("data");
            responseData["system_status"] = statusObj;

            sendJSONResponse(request, 200, true, "System status retrieved", &responseData);

        } catch (const std::exception& e) {
            handle500(request, "Failed to get system status: " + String(e.what()));
        }
    });

    Serial.println("✅ WebServerManager: System API registered");
}

void WebServerManager::registerConfigAPI() {
    // GET /api/config - 获取配置
    m_webServer->on("/api/config", HTTP_GET, [this](AsyncWebServerRequest* request) {
        logRequest(request);
        sendJSONResponse(request, 200, true, "Config API not fully implemented");
    });

    Serial.println("✅ WebServerManager: Config API registered");
}

void WebServerManager::registerOTAAPI() {
    // GET /api/ota/status - 获取OTA状态
    m_webServer->on("/api/ota/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        logRequest(request);
        sendJSONResponse(request, 200, true, "OTA API not fully implemented");
    });

    Serial.println("✅ WebServerManager: OTA API registered");
}

// ==================== 中间件实现 ====================

void WebServerManager::addCORSMiddleware(AsyncWebServerResponse* response) {
    if (m_serverConfig.enableCORS) {
        response->addHeader("Access-Control-Allow-Origin", m_serverConfig.allowedOrigins);
        response->addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response->addHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
        response->addHeader("Access-Control-Max-Age", "86400");
    }
}

void WebServerManager::addSecurityHeaders(AsyncWebServerResponse* response) {
    if (m_serverConfig.enableSecurity) {
        response->addHeader("X-Content-Type-Options", "nosniff");
        response->addHeader("X-Frame-Options", "DENY");
        response->addHeader("X-XSS-Protection", "1; mode=block");
        response->addHeader("Referrer-Policy", "strict-origin-when-cross-origin");
        response->addHeader("Server", m_serverConfig.serverName);
    }
}

void WebServerManager::addCacheHeaders(AsyncWebServerResponse* response, uint32_t maxAge) {
    if (m_serverConfig.enableCache) {
        uint32_t cacheAge = maxAge > 0 ? maxAge : m_serverConfig.cacheMaxAge;
        response->addHeader("Cache-Control", "public, max-age=" + String(cacheAge));
        response->addHeader("ETag", String(millis()));
    }
}

void WebServerManager::logRequest(AsyncWebServerRequest* request, int responseCode) {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_requestStats.totalRequests++;
    m_requestStats.lastRequestTime = millis();

    if (request->url().startsWith("/api/")) {
        m_requestStats.apiRequests++;
    } else {
        m_requestStats.staticFileRequests++;
    }

    if (responseCode >= 400) {
        m_requestStats.errorRequests++;
    }

    String logMessage = String(request->methodToString()) + " " + request->url() +
                       " from " + getClientIP(request) +
                       " - " + String(responseCode);

    if (m_systemService) {
        m_systemService->addLogEntry(
            responseCode >= 400 ? SystemService::LogLevel::WARNING : SystemService::LogLevel::INFO,
            "WebServerManager",
            logMessage
        );
    }
}

// ==================== 错误处理实现 ====================

void WebServerManager::handle404(AsyncWebServerRequest* request) {
    logRequest(request, 404);
    sendJSONResponse(request, 404, false, "Resource not found: " + request->url());
}

void WebServerManager::handle500(AsyncWebServerRequest* request, const String& error) {
    logRequest(request, 500);
    sendJSONResponse(request, 500, false, "Internal server error: " + error);
}

void WebServerManager::sendJSONResponse(AsyncWebServerRequest* request, int code, bool success,
                                       const String& message, JsonObject* data) {
    DynamicJsonDocument doc(1024);

    doc["success"] = success;
    doc["message"] = message;
    doc["timestamp"] = millis();

    if (data) {
        doc["data"] = *data;
    }

    String response;
    serializeJson(doc, response);

    AsyncWebServerResponse* webResponse = request->beginResponse(code, "application/json", response);
    addCORSMiddleware(webResponse);
    addSecurityHeaders(webResponse);
    request->send(webResponse);
}
