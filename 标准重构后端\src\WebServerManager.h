#pragma once

#include <ESPAsyncWebServer.h>
#include <LittleFS.h>
#include "core/DataStructures.h"
#include "services/SignalService.h"
#include "services/TaskService.h"
#include "services/TimerService.h"
#include "services/SystemService.h"
#include "network/WSManager.h"
#include <functional>
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - Web服务器管理器
 * 
 * 功能：
 * 1. Web服务器生命周期管理
 * 2. 静态文件服务
 * 3. API路由集成管理
 * 4. 中间件和安全控制
 * 5. 错误处理和日志记录
 * 
 * 设计原则：
 * - 统一管理：集中管理所有Web服务
 * - 模块化：分离的API控制器集成
 * - 安全控制：统一的安全策略
 * - 性能优化：静态资源缓存和压缩
 * - 错误处理：完整的错误处理机制
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 */

class WebServerManager {
public:
    // 服务器配置结构
    struct ServerConfig {
        uint16_t port;                 // 服务器端口
        bool enableCORS;               // 是否启用跨域
        String allowedOrigins;         // 允许的源
        bool enableGzip;               // 是否启用Gzip压缩
        bool enableCache;              // 是否启用缓存
        uint32_t cacheMaxAge;          // 缓存最大时间（秒）
        bool enableSecurity;           // 是否启用安全头
        String serverName;             // 服务器名称
        
        ServerConfig() : port(80), enableCORS(true), allowedOrigins("*"),
                        enableGzip(true), enableCache(true), cacheMaxAge(86400),
                        enableSecurity(true), serverName("ESP32-IR-System") {}
    };
    
    // 请求统计结构
    struct RequestStats {
        uint32_t totalRequests;        // 总请求数
        uint32_t staticFileRequests;   // 静态文件请求数
        uint32_t apiRequests;          // API请求数
        uint32_t errorRequests;        // 错误请求数
        uint32_t cacheHits;            // 缓存命中数
        Timestamp lastRequestTime;     // 最后请求时间
        
        RequestStats() : totalRequests(0), staticFileRequests(0), apiRequests(0),
                        errorRequests(0), cacheHits(0), lastRequestTime(0) {}
    };

private:
    // Web服务器实例
    AsyncWebServer* m_webServer;
    
    // 依赖服务
    SignalService* m_signalService;
    TaskService* m_taskService;
    TimerService* m_timerService;
    SystemService* m_systemService;
    WSManager* m_wsManager;
    
    // 配置
    ServerConfig m_serverConfig;
    
    // 状态
    bool m_initialized;
    bool m_running;
    
    // 统计信息
    RequestStats m_requestStats;
    
    // 线程安全
    mutable std::mutex m_mutex;

public:
    /**
     * 构造函数
     * @param signalService 信号服务
     * @param taskService 任务服务
     * @param timerService 定时器服务
     * @param systemService 系统服务
     * @param wsManager WebSocket管理器
     */
    WebServerManager(SignalService* signalService, TaskService* taskService,
                     TimerService* timerService, SystemService* systemService,
                     WSManager* wsManager);
    
    /**
     * 析构函数
     */
    ~WebServerManager();
    
    // ==================== 生命周期管理 ====================
    
    /**
     * 初始化Web服务器管理器
     * @return 是否成功
     */
    bool initialize();
    
    /**
     * 启动Web服务器
     * @return 是否成功
     */
    bool start();
    
    /**
     * 停止Web服务器
     * @return 是否成功
     */
    bool stop();
    
    /**
     * 清理资源
     */
    void cleanup();
    
    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return m_initialized; }
    
    /**
     * 检查是否正在运行
     * @return 是否正在运行
     */
    bool isRunning() const { return m_running; }
    
    // ==================== 路由管理 ====================
    
    /**
     * 注册所有路由
     */
    void registerAllRoutes();
    
    /**
     * 注册静态文件路由
     */
    void registerStaticRoutes();
    
    /**
     * 注册API路由
     */
    void registerAPIRoutes();
    
    /**
     * 注册信号管理API
     */
    void registerSignalAPI();
    
    /**
     * 注册任务管理API
     */
    void registerTaskAPI();
    
    /**
     * 注册定时器管理API
     */
    void registerTimerAPI();
    
    /**
     * 注册系统管理API
     */
    void registerSystemAPI();
    
    /**
     * 注册配置管理API
     */
    void registerConfigAPI();
    
    /**
     * 注册OTA管理API
     */
    void registerOTAAPI();
    
    // ==================== 中间件 ====================
    
    /**
     * 添加CORS中间件
     * @param response 响应对象
     */
    void addCORSMiddleware(AsyncWebServerResponse* response);
    
    /**
     * 添加安全头中间件
     * @param response 响应对象
     */
    void addSecurityHeaders(AsyncWebServerResponse* response);
    
    /**
     * 添加缓存头中间件
     * @param response 响应对象
     * @param maxAge 缓存时间（秒）
     */
    void addCacheHeaders(AsyncWebServerResponse* response, uint32_t maxAge = 0);
    
    /**
     * 记录请求日志
     * @param request 请求对象
     * @param responseCode 响应代码
     */
    void logRequest(AsyncWebServerRequest* request, int responseCode = 200);
    
    // ==================== 错误处理 ====================
    
    /**
     * 处理404错误
     * @param request 请求对象
     */
    void handle404(AsyncWebServerRequest* request);
    
    /**
     * 处理500错误
     * @param request 请求对象
     * @param error 错误信息
     */
    void handle500(AsyncWebServerRequest* request, const String& error);
    
    /**
     * 发送JSON响应
     * @param request 请求对象
     * @param code HTTP状态码
     * @param success 是否成功
     * @param message 响应消息
     * @param data 响应数据（可选）
     */
    void sendJSONResponse(AsyncWebServerRequest* request, int code, bool success, 
                         const String& message, JsonObject* data = nullptr);
    
    // ==================== 静态文件服务 ====================
    
    /**
     * 服务静态文件
     * @param request 请求对象
     * @param filename 文件名
     */
    void serveStaticFile(AsyncWebServerRequest* request, const String& filename);
    
    /**
     * 检查文件是否存在
     * @param path 文件路径
     * @return 是否存在
     */
    bool fileExists(const String& path);
    
    /**
     * 获取文件MIME类型
     * @param filename 文件名
     * @return MIME类型
     */
    String getMimeType(const String& filename);
    
    // ==================== 配置管理 ====================
    
    /**
     * 更新服务器配置
     * @param config 新配置
     */
    void updateServerConfig(const ServerConfig& config);
    
    /**
     * 获取服务器配置
     * @return 服务器配置
     */
    const ServerConfig& getServerConfig() const { return m_serverConfig; }
    
    /**
     * 获取Web服务器实例
     * @return Web服务器指针
     */
    AsyncWebServer* getWebServer() { return m_webServer; }
    
    // ==================== 统计和监控 ====================
    
    /**
     * 获取请求统计信息
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getRequestStatistics(JsonDocument& doc);
    
    /**
     * 重置请求统计
     */
    void resetRequestStatistics();
    
    /**
     * 获取服务器状态
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getServerStatus(JsonDocument& doc);

private:
    // ==================== 内部实现方法 ====================
    
    /**
     * 初始化Web服务器实例
     * @return 是否成功
     */
    bool initializeWebServer();
    
    /**
     * 设置默认处理器
     */
    void setupDefaultHandlers();
    
    /**
     * 验证请求参数
     * @param request 请求对象
     * @param requiredParams 必需参数列表
     * @return 是否验证通过
     */
    bool validateRequestParams(AsyncWebServerRequest* request, const std::vector<String>& requiredParams);
    
    /**
     * 解析JSON请求体
     * @param data 请求数据
     * @param len 数据长度
     * @param doc JSON文档
     * @return 是否解析成功
     */
    bool parseJSONBody(uint8_t* data, size_t len, JsonDocument& doc);
    
    /**
     * 获取客户端IP
     * @param request 请求对象
     * @return 客户端IP
     */
    String getClientIP(AsyncWebServerRequest* request);
};
