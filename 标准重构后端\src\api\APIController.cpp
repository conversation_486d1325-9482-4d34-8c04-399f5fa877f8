#include "APIController.h"
#include <Arduino.h>

APIController::APIController(AsyncWebServer* webServer, SignalService* signalService,
                            TaskService* taskService, TimerService* timerService,
                            SystemService* systemService, WSManager* wsManager)
    : m_webServer(webServer), m_signalService(signalService), m_taskService(taskService),
      m_timerService(timerService), m_systemService(systemService), m_wsManager(wsManager),
      m_initialized(false) {
}

APIController::~APIController() {
    cleanup();
}

bool APIController::initialize() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        return true;
    }
    
    // 验证依赖组件
    if (!m_webServer || !m_signalService || !m_taskService || !m_timerService || !m_systemService) {
        Serial.println("❌ APIController: Missing required dependencies");
        return false;
    }
    
    // 检查依赖组件是否已初始化
    if (!m_signalService->isInitialized() || !m_taskService->isInitialized() || 
        !m_timerService->isInitialized() || !m_systemService->isInitialized()) {
        Serial.println("❌ APIController: Dependencies not initialized");
        return false;
    }
    
    // 注册所有API路由
    registerRoutes();
    
    m_initialized = true;
    
    Serial.println("✅ APIController: Initialized successfully");
    return true;
}

void APIController::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        // 清理限流映射
        m_rateLimitMap.clear();
        
        m_initialized = false;
        
        Serial.println("✅ APIController: Cleanup completed");
    }
}

void APIController::registerRoutes() {
    // 注册各模块的API路由
    registerSignalRoutes();
    registerTaskRoutes();
    registerTimerRoutes();
    registerSystemRoutes();
    registerConfigRoutes();
    registerOTARoutes();
    
    // 注册通用路由
    m_webServer->onNotFound([this](AsyncWebServerRequest* request) {
        this->handleNotFound(request);
    });
    
    // 注册OPTIONS处理（CORS预检）
    m_webServer->on("/*", HTTP_OPTIONS, [this](AsyncWebServerRequest* request) {
        this->handleOptionsRequest(request);
    });
    
    Serial.println("✅ APIController: All routes registered");
}

void APIController::registerSignalRoutes() {
    // GET /api/signals - 获取所有信号
    m_webServer->on("/api/signals", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetSignals(request);
    });
    
    // POST /api/signals - 创建新信号
    m_webServer->on("/api/signals", HTTP_POST, 
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleCreateSignal(request, data, len, index, total);
        }
    );
    
    // GET /api/signals/{id} - 获取特定信号
    m_webServer->on("^\\/api\\/signals\\/([0-9]+)$", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetSignal(request);
    });
    
    // PUT /api/signals/{id} - 更新信号
    m_webServer->on("^\\/api\\/signals\\/([0-9]+)$", HTTP_PUT,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleUpdateSignal(request, data, len, index, total);
        }
    );
    
    // DELETE /api/signals/{id} - 删除信号
    m_webServer->on("^\\/api\\/signals\\/([0-9]+)$", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        this->handleDeleteSignal(request);
    });
    
    // POST /api/signals/send - 发送信号
    m_webServer->on("/api/signals/send", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleSendSignal(request, data, len, index, total);
        }
    );
    
    // POST /api/signals/{id}/send - 发送特定信号
    m_webServer->on("^\\/api\\/signals\\/([0-9]+)\\/send$", HTTP_POST, [this](AsyncWebServerRequest* request) {
        this->handleSendSpecificSignal(request);
    });
    
    // POST /api/signals/batch-send - 批量发送信号
    m_webServer->on("/api/signals/batch-send", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleBatchSendSignals(request, data, len, index, total);
        }
    );
    
    // POST /api/signals/batch-delete - 批量删除信号
    m_webServer->on("/api/signals/batch-delete", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleBatchDeleteSignals(request, data, len, index, total);
        }
    );
    
    // POST /api/signals/learn/start - 开始学习信号
    m_webServer->on("/api/signals/learn/start", HTTP_POST, [this](AsyncWebServerRequest* request) {
        this->handleStartLearning(request);
    });
    
    // POST /api/signals/learn/stop - 停止学习信号
    m_webServer->on("/api/signals/learn/stop", HTTP_POST, [this](AsyncWebServerRequest* request) {
        this->handleStopLearning(request);
    });
    
    // POST /api/signals/learn/save - 保存学习的信号
    m_webServer->on("/api/signals/learn/save", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleSaveLearning(request, data, len, index, total);
        }
    );
    
    // GET /api/signals/learn/status - 获取学习状态
    m_webServer->on("/api/signals/learn/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetLearningStatus(request);
    });
    
    // POST /api/signals/import - 导入信号
    m_webServer->on("/api/signals/import", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleImportSignals(request, data, len, index, total);
        }
    );
    
    // GET /api/signals/export - 导出信号
    m_webServer->on("/api/signals/export", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleExportSignals(request);
    });
    
    // POST /api/signals/export/selected - 导出选中信号
    m_webServer->on("/api/signals/export/selected", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleExportSelectedSignals(request, data, len, index, total);
        }
    );
    
    // GET /api/signals/search - 搜索信号
    m_webServer->on("/api/signals/search", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleSearchSignals(request);
    });
    
    // GET /api/signals/stats - 获取信号统计
    m_webServer->on("/api/signals/stats", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetSignalStats(request);
    });
    
    // GET /api/signals/controller/status - 获取控制器状态
    m_webServer->on("/api/signals/controller/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetControllerStatus(request);
    });
    
    Serial.println("✅ APIController: Signal routes registered");
}

void APIController::registerTaskRoutes() {
    // GET /api/tasks - 获取所有任务
    m_webServer->on("/api/tasks", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetTasks(request);
    });
    
    // POST /api/tasks - 创建新任务
    m_webServer->on("/api/tasks", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleCreateTask(request, data, len, index, total);
        }
    );
    
    Serial.println("✅ APIController: Task routes registered");
}

void APIController::registerTimerRoutes() {
    // GET /api/timers - 获取所有定时器
    m_webServer->on("/api/timers", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetTimers(request);
    });
    
    // POST /api/timers - 创建新定时器
    m_webServer->on("/api/timers", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleCreateTimer(request, data, len, index, total);
        }
    );
    
    // GET /api/timers/{id} - 获取特定定时器
    m_webServer->on("^\\/api\\/timers\\/([0-9]+)$", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetTimer(request);
    });
    
    // PUT /api/timers/{id} - 更新定时器
    m_webServer->on("^\\/api\\/timers\\/([0-9]+)$", HTTP_PUT,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleUpdateTimer(request, data, len, index, total);
        }
    );
    
    // DELETE /api/timers/{id} - 删除定时器
    m_webServer->on("^\\/api\\/timers\\/([0-9]+)$", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        this->handleDeleteTimer(request);
    });
    
    // POST /api/timers/{id}/toggle - 切换定时器状态
    m_webServer->on("^\\/api\\/timers\\/([0-9]+)\\/toggle$", HTTP_POST, [this](AsyncWebServerRequest* request) {
        this->handleToggleTimer(request);
    });
    
    Serial.println("✅ APIController: Timer routes registered");
}

void APIController::registerSystemRoutes() {
    // GET /api/system/status - 获取系统状态
    m_webServer->on("/api/system/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetSystemStatus(request);
    });

    // GET /api/system/performance - 获取系统性能
    m_webServer->on("/api/system/performance", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetSystemPerformance(request);
    });

    // GET /api/system/hardware - 获取硬件信息
    m_webServer->on("/api/system/hardware", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetHardwareInfo(request);
    });

    // POST /api/system/reset - 系统重置
    m_webServer->on("/api/system/reset", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleSystemReset(request, data, len, index, total);
        }
    );

    // GET /api/system/logs - 获取系统日志
    m_webServer->on("/api/system/logs", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetSystemLogs(request);
    });

    // POST /api/system/logs - 保存系统日志
    m_webServer->on("/api/system/logs", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleSaveSystemLogs(request, data, len, index, total);
        }
    );

    Serial.println("✅ APIController: System routes registered");
}

void APIController::registerConfigRoutes() {
    // GET /api/config - 获取配置
    m_webServer->on("/api/config", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetConfig(request);
    });

    // POST /api/config - 更新配置
    m_webServer->on("/api/config", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleUpdateConfig(request, data, len, index, total);
        }
    );

    // GET /api/config/export - 导出配置
    m_webServer->on("/api/config/export", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleExportConfig(request);
    });

    // POST /api/config/import - 导入配置
    m_webServer->on("/api/config/import", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleImportConfig(request, data, len, index, total);
        }
    );

    // POST /api/config/reset - 重置配置
    m_webServer->on("/api/config/reset", HTTP_POST, [this](AsyncWebServerRequest* request) {
        this->handleResetConfig(request);
    });

    Serial.println("✅ APIController: Config routes registered");
}

void APIController::registerOTARoutes() {
    // GET /api/ota/status - 获取OTA状态
    m_webServer->on("/api/ota/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetOTAStatus(request);
    });

    // POST /api/ota/login - OTA登录
    m_webServer->on("/api/ota/login", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleOTALogin(request, data, len, index, total);
        }
    );

    // POST /api/ota/firmware - 固件更新
    m_webServer->on("/api/ota/firmware", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleOTAFirmware(request, data, len, index, total);
        }
    );

    // POST /api/ota/filesystem - 文件系统更新
    m_webServer->on("/api/ota/filesystem", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // 处理完成后的回调
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleOTAFilesystem(request, data, len, index, total);
        }
    );

    Serial.println("✅ APIController: OTA routes registered");
}

// ==================== 信号管理API实现 ====================

void APIController::handleGetSignals(AsyncWebServerRequest* request) {
    if (!checkAuthentication(request) || !checkRateLimit(getClientIP(request))) {
        return;
    }

    try {
        auto signals = m_signalService->getAllSignals();

        DynamicJsonDocument doc(signals.size() * 512 + 1024);
        JsonArray signalsArray = JSONConverter::signalsToJsonArray(signals, doc);

        JsonObject responseData = doc.createNestedObject("response");
        responseData["signals"] = signalsArray;
        responseData["count"] = signals.size();

        sendSuccessResponse(request, "Signals retrieved successfully", &responseData);
        logAPIRequest(request, true);

    } catch (const std::exception& e) {
        sendErrorResponse(request, ErrorCode::INTERNAL_ERROR, "Failed to retrieve signals: " + String(e.what()), 500);
        logAPIRequest(request, false, ErrorCode::INTERNAL_ERROR);
    }
}

void APIController::handleCreateSignal(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    if (!checkAuthentication(request) || !checkRateLimit(getClientIP(request))) {
        return;
    }

    if (index + len != total) {
        return; // 等待所有数据接收完成
    }

    try {
        DynamicJsonDocument doc(1024);
        if (!parseJSONBody(data, len, doc)) {
            sendErrorResponse(request, ErrorCode::INVALID_PARAMETER, "Invalid JSON format");
            logAPIRequest(request, false, ErrorCode::INVALID_PARAMETER);
            return;
        }

        auto signalResult = JSONConverter::signalFromJson(doc.as<JsonObject>());
        if (!signalResult.isSuccess()) {
            sendErrorResponse(request, ErrorCode::INVALID_PARAMETER, "Invalid signal data: " + signalResult.getErrorMessage());
            logAPIRequest(request, false, ErrorCode::INVALID_PARAMETER);
            return;
        }

        auto createResult = m_signalService->createSignal(signalResult.getValue());
        if (createResult.isSuccess()) {
            DynamicJsonDocument responseDoc(1024);
            JsonObject signalObj = JSONConverter::signalToJson(createResult.getValue(), responseDoc);

            JsonObject responseData = responseDoc.createNestedObject("response");
            responseData["signal"] = signalObj;

            sendSuccessResponse(request, "Signal created successfully", &responseData);
            logAPIRequest(request, true);
        } else {
            sendErrorResponse(request, createResult.getErrorCode(), "Failed to create signal: " + createResult.getErrorMessage());
            logAPIRequest(request, false, createResult.getErrorCode());
        }

    } catch (const std::exception& e) {
        sendErrorResponse(request, ErrorCode::INTERNAL_ERROR, "Internal error: " + String(e.what()), 500);
        logAPIRequest(request, false, ErrorCode::INTERNAL_ERROR);
    }
}

void APIController::handleGetSignal(AsyncWebServerRequest* request) {
    if (!checkAuthentication(request) || !checkRateLimit(getClientIP(request))) {
        return;
    }

    SignalID signalId = validateSignalID(request);
    if (signalId == INVALID_ID) {
        sendErrorResponse(request, ErrorCode::INVALID_PARAMETER, "Invalid signal ID");
        logAPIRequest(request, false, ErrorCode::INVALID_PARAMETER);
        return;
    }

    try {
        auto result = m_signalService->getSignal(signalId);
        if (result.isSuccess()) {
            DynamicJsonDocument doc(1024);
            JsonObject signalObj = JSONConverter::signalToJson(result.getValue(), doc);

            JsonObject responseData = doc.createNestedObject("response");
            responseData["signal"] = signalObj;

            sendSuccessResponse(request, "Signal retrieved successfully", &responseData);
            logAPIRequest(request, true);
        } else {
            sendErrorResponse(request, result.getErrorCode(), "Signal not found: " + result.getErrorMessage(), 404);
            logAPIRequest(request, false, result.getErrorCode());
        }

    } catch (const std::exception& e) {
        sendErrorResponse(request, ErrorCode::INTERNAL_ERROR, "Failed to retrieve signal: " + String(e.what()), 500);
        logAPIRequest(request, false, ErrorCode::INTERNAL_ERROR);
    }
}

void APIController::handleUpdateSignal(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    if (!checkAuthentication(request) || !checkRateLimit(getClientIP(request))) {
        return;
    }

    if (index + len != total) {
        return; // 等待所有数据接收完成
    }

    SignalID signalId = validateSignalID(request);
    if (signalId == INVALID_ID) {
        sendErrorResponse(request, ErrorCode::INVALID_PARAMETER, "Invalid signal ID");
        logAPIRequest(request, false, ErrorCode::INVALID_PARAMETER);
        return;
    }

    try {
        DynamicJsonDocument doc(1024);
        if (!parseJSONBody(data, len, doc)) {
            sendErrorResponse(request, ErrorCode::INVALID_PARAMETER, "Invalid JSON format");
            logAPIRequest(request, false, ErrorCode::INVALID_PARAMETER);
            return;
        }

        auto signalResult = JSONConverter::signalFromJson(doc.as<JsonObject>());
        if (!signalResult.isSuccess()) {
            sendErrorResponse(request, ErrorCode::INVALID_PARAMETER, "Invalid signal data: " + signalResult.getErrorMessage());
            logAPIRequest(request, false, ErrorCode::INVALID_PARAMETER);
            return;
        }

        SignalData signal = signalResult.getValue();
        signal.id = signalId; // 确保使用URL中的ID

        auto updateResult = m_signalService->updateSignal(signal);
        if (updateResult.isSuccess()) {
            DynamicJsonDocument responseDoc(1024);
            JsonObject signalObj = JSONConverter::signalToJson(updateResult.getValue(), responseDoc);

            JsonObject responseData = responseDoc.createNestedObject("response");
            responseData["signal"] = signalObj;

            sendSuccessResponse(request, "Signal updated successfully", &responseData);
            logAPIRequest(request, true);
        } else {
            sendErrorResponse(request, updateResult.getErrorCode(), "Failed to update signal: " + updateResult.getErrorMessage());
            logAPIRequest(request, false, updateResult.getErrorCode());
        }

    } catch (const std::exception& e) {
        sendErrorResponse(request, ErrorCode::INTERNAL_ERROR, "Internal error: " + String(e.what()), 500);
        logAPIRequest(request, false, ErrorCode::INTERNAL_ERROR);
    }
}

void APIController::handleDeleteSignal(AsyncWebServerRequest* request) {
    if (!checkAuthentication(request) || !checkRateLimit(getClientIP(request))) {
        return;
    }

    SignalID signalId = validateSignalID(request);
    if (signalId == INVALID_ID) {
        sendErrorResponse(request, ErrorCode::INVALID_PARAMETER, "Invalid signal ID");
        logAPIRequest(request, false, ErrorCode::INVALID_PARAMETER);
        return;
    }

    try {
        bool success = m_signalService->deleteSignal(signalId);
        if (success) {
            sendSuccessResponse(request, "Signal deleted successfully");
            logAPIRequest(request, true);
        } else {
            sendErrorResponse(request, ErrorCode::RESOURCE_NOT_FOUND, "Failed to delete signal", 404);
            logAPIRequest(request, false, ErrorCode::RESOURCE_NOT_FOUND);
        }

    } catch (const std::exception& e) {
        sendErrorResponse(request, ErrorCode::INTERNAL_ERROR, "Failed to delete signal: " + String(e.what()), 500);
        logAPIRequest(request, false, ErrorCode::INTERNAL_ERROR);
    }
}

// ==================== 工具方法实现 ====================

void APIController::sendSuccessResponse(AsyncWebServerRequest* request, const String& message, JsonObject* data) {
    DynamicJsonDocument doc(1024);

    doc["success"] = true;
    doc["message"] = message;
    doc["timestamp"] = millis();

    if (data) {
        doc["data"] = *data;
    }

    String response;
    serializeJson(doc, response);

    AsyncWebServerResponse* webResponse = request->beginResponse(200, "application/json", response);
    addCORSHeaders(webResponse);
    request->send(webResponse);

    m_requestStats.successfulRequests++;
}

void APIController::sendErrorResponse(AsyncWebServerRequest* request, ErrorCode errorCode, const String& message, int httpCode) {
    DynamicJsonDocument doc(512);

    doc["success"] = false;
    doc["error_code"] = static_cast<int>(errorCode);
    doc["message"] = message;
    doc["timestamp"] = millis();

    String response;
    serializeJson(doc, response);

    AsyncWebServerResponse* webResponse = request->beginResponse(httpCode, "application/json", response);
    addCORSHeaders(webResponse);
    request->send(webResponse);

    m_requestStats.failedRequests++;
}

bool APIController::validateRequestParams(AsyncWebServerRequest* request, const std::vector<String>& requiredParams) {
    for (const String& param : requiredParams) {
        if (!request->hasParam(param)) {
            sendErrorResponse(request, ErrorCode::INVALID_PARAMETER, "Missing required parameter: " + param);
            return false;
        }
    }
    return true;
}

bool APIController::parseJSONBody(uint8_t* data, size_t len, JsonDocument& doc) {
    if (len == 0) {
        return false;
    }

    DeserializationError error = deserializeJson(doc, data, len);
    return error == DeserializationError::Ok;
}

bool APIController::checkAuthentication(AsyncWebServerRequest* request) {
    if (!m_apiConfig.enableAuthentication) {
        return true;
    }

    String authHeader = "";
    if (request->hasHeader("Authorization")) {
        authHeader = request->getHeader("Authorization")->value();
    } else if (request->hasParam("api_key")) {
        authHeader = "Bearer " + request->getParam("api_key")->value();
    }

    if (authHeader.startsWith("Bearer ")) {
        String token = authHeader.substring(7);
        if (token == m_apiConfig.apiKey) {
            return true;
        }
    }

    sendErrorResponse(request, ErrorCode::AUTHENTICATION_FAILED, "Authentication required", 401);
    m_requestStats.authenticationFailures++;
    return false;
}

bool APIController::checkRateLimit(const String& clientIP) {
    if (!m_apiConfig.enableRateLimit) {
        return true;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    Timestamp currentTime = millis();
    Timestamp windowStart = currentTime - 60000; // 1分钟窗口

    // 清理过期记录
    cleanupRateLimitMap();

    // 获取客户端的请求记录
    auto& requests = m_rateLimitMap[clientIP];

    // 移除窗口外的请求
    requests.erase(std::remove_if(requests.begin(), requests.end(),
                                 [windowStart](Timestamp time) { return time < windowStart; }),
                  requests.end());

    // 检查是否超过限制
    if (requests.size() >= m_apiConfig.rateLimit) {
        m_requestStats.rateLimitExceeded++;
        return false;
    }

    // 添加当前请求
    requests.push_back(currentTime);

    return true;
}

void APIController::addCORSHeaders(AsyncWebServerResponse* response) {
    if (m_apiConfig.enableCORS) {
        response->addHeader("Access-Control-Allow-Origin", m_apiConfig.allowedOrigins);
        response->addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response->addHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
        response->addHeader("Access-Control-Max-Age", "86400");
    }
}

void APIController::logAPIRequest(AsyncWebServerRequest* request, bool success, ErrorCode errorCode) {
    m_requestStats.totalRequests++;
    m_requestStats.lastRequestTime = millis();

    String logMessage = String(request->methodToString()) + " " + request->url() +
                       " from " + getClientIP(request) +
                       " - " + (success ? "SUCCESS" : "FAILED");

    if (!success) {
        logMessage += " (Error: " + String(static_cast<int>(errorCode)) + ")";
    }

    if (m_systemService) {
        m_systemService->addLogEntry(
            success ? SystemService::LogLevel::INFO : SystemService::LogLevel::WARNING,
            "APIController",
            logMessage
        );
    }
}

String APIController::getClientIP(AsyncWebServerRequest* request) {
    // 检查X-Forwarded-For头（代理情况）
    if (request->hasHeader("X-Forwarded-For")) {
        return request->getHeader("X-Forwarded-For")->value();
    }

    // 检查X-Real-IP头
    if (request->hasHeader("X-Real-IP")) {
        return request->getHeader("X-Real-IP")->value();
    }

    // 返回直接连接的IP
    return request->client()->remoteIP().toString();
}

JsonObject APIController::getRequestStatistics(JsonDocument& doc) {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject stats = doc.createNestedObject("api_statistics");

    stats["total_requests"] = m_requestStats.totalRequests;
    stats["successful_requests"] = m_requestStats.successfulRequests;
    stats["failed_requests"] = m_requestStats.failedRequests;
    stats["authentication_failures"] = m_requestStats.authenticationFailures;
    stats["rate_limit_exceeded"] = m_requestStats.rateLimitExceeded;
    stats["last_request_time"] = m_requestStats.lastRequestTime;

    // 计算成功率
    if (m_requestStats.totalRequests > 0) {
        stats["success_rate"] = (float)m_requestStats.successfulRequests / m_requestStats.totalRequests;
    }

    return stats;
}

void APIController::resetRequestStatistics() {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_requestStats = RequestStats();

    if (m_systemService) {
        m_systemService->addLogEntry(SystemService::LogLevel::INFO, "APIController", "Request statistics reset");
    }
}

void APIController::updateAPIConfig(const APIConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_apiConfig = config;

    if (m_systemService) {
        m_systemService->addLogEntry(SystemService::LogLevel::INFO, "APIController", "API configuration updated");
    }
}

// ==================== 内部实现方法 ====================

void APIController::handleOptionsRequest(AsyncWebServerRequest* request) {
    AsyncWebServerResponse* response = request->beginResponse(200);
    addCORSHeaders(response);
    request->send(response);
}

void APIController::handleNotFound(AsyncWebServerRequest* request) {
    DynamicJsonDocument doc(256);

    doc["success"] = false;
    doc["error_code"] = static_cast<int>(ErrorCode::RESOURCE_NOT_FOUND);
    doc["message"] = "API endpoint not found: " + request->url();
    doc["timestamp"] = millis();

    String response;
    serializeJson(doc, response);

    AsyncWebServerResponse* webResponse = request->beginResponse(404, "application/json", response);
    addCORSHeaders(webResponse);
    request->send(webResponse);

    logAPIRequest(request, false, ErrorCode::RESOURCE_NOT_FOUND);
}

void APIController::cleanupRateLimitMap() {
    Timestamp currentTime = millis();
    Timestamp cutoffTime = currentTime - 60000; // 1分钟前

    for (auto it = m_rateLimitMap.begin(); it != m_rateLimitMap.end();) {
        auto& requests = it->second;

        // 移除过期的请求记录
        requests.erase(std::remove_if(requests.begin(), requests.end(),
                                     [cutoffTime](Timestamp time) { return time < cutoffTime; }),
                      requests.end());

        // 如果没有活跃请求，移除整个条目
        if (requests.empty()) {
            it = m_rateLimitMap.erase(it);
        } else {
            ++it;
        }
    }
}

SignalID APIController::validateSignalID(AsyncWebServerRequest* request) {
    if (request->pathArg(0).isEmpty()) {
        return INVALID_ID;
    }

    long id = request->pathArg(0).toInt();
    if (id <= 0 || id > UINT32_MAX) {
        return INVALID_ID;
    }

    return static_cast<SignalID>(id);
}

TaskID APIController::validateTaskID(AsyncWebServerRequest* request) {
    if (request->pathArg(0).isEmpty()) {
        return INVALID_ID;
    }

    long id = request->pathArg(0).toInt();
    if (id <= 0 || id > UINT32_MAX) {
        return INVALID_ID;
    }

    return static_cast<TaskID>(id);
}

TimerID APIController::validateTimerID(AsyncWebServerRequest* request) {
    if (request->pathArg(0).isEmpty()) {
        return INVALID_ID;
    }

    long id = request->pathArg(0).toInt();
    if (id <= 0 || id > UINT32_MAX) {
        return INVALID_ID;
    }

    return static_cast<TimerID>(id);
}

// ==================== 简化的API实现（由于篇幅限制） ====================

void APIController::handleSendSignal(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    // 实现信号发送逻辑
    sendSuccessResponse(request, "Signal send request processed");
}

void APIController::handleSendSpecificSignal(AsyncWebServerRequest* request) {
    SignalID signalId = validateSignalID(request);
    if (signalId != INVALID_ID && m_signalService->sendSignal(signalId)) {
        sendSuccessResponse(request, "Signal sent successfully");
    } else {
        sendErrorResponse(request, ErrorCode::OPERATION_FAILED, "Failed to send signal");
    }
}

void APIController::handleStartLearning(AsyncWebServerRequest* request) {
    if (m_signalService->startLearning()) {
        sendSuccessResponse(request, "Learning started successfully");
    } else {
        sendErrorResponse(request, ErrorCode::OPERATION_FAILED, "Failed to start learning");
    }
}

void APIController::handleStopLearning(AsyncWebServerRequest* request) {
    if (m_signalService->stopLearning()) {
        sendSuccessResponse(request, "Learning stopped successfully");
    } else {
        sendErrorResponse(request, ErrorCode::OPERATION_FAILED, "Failed to stop learning");
    }
}

void APIController::handleGetLearningStatus(AsyncWebServerRequest* request) {
    DynamicJsonDocument doc(512);
    JsonObject responseData = doc.createNestedObject("response");
    responseData["status"] = m_signalService->getLearningStatus();
    responseData["progress"] = m_signalService->getLearningProgress();

    sendSuccessResponse(request, "Learning status retrieved", &responseData);
}

void APIController::handleGetTasks(AsyncWebServerRequest* request) {
    auto tasks = m_taskService->getAllTasks();

    DynamicJsonDocument doc(tasks.size() * 512 + 1024);
    JsonArray tasksArray = JSONConverter::tasksToJsonArray(tasks, doc);

    JsonObject responseData = doc.createNestedObject("response");
    responseData["tasks"] = tasksArray;
    responseData["count"] = tasks.size();

    sendSuccessResponse(request, "Tasks retrieved successfully", &responseData);
}

void APIController::handleGetTimers(AsyncWebServerRequest* request) {
    auto timers = m_timerService->getAllTimers();

    DynamicJsonDocument doc(timers.size() * 512 + 1024);
    JsonArray timersArray = JSONConverter::timersToJsonArray(timers, doc);

    JsonObject responseData = doc.createNestedObject("response");
    responseData["timers"] = timersArray;
    responseData["count"] = timers.size();

    sendSuccessResponse(request, "Timers retrieved successfully", &responseData);
}

void APIController::handleGetSystemStatus(AsyncWebServerRequest* request) {
    auto status = m_systemService->getSystemStatus();

    DynamicJsonDocument doc(1024);
    JsonObject statusObj = JSONConverter::systemStatusToJson(status, doc);

    JsonObject responseData = doc.createNestedObject("response");
    responseData["system_status"] = statusObj;

    sendSuccessResponse(request, "System status retrieved", &responseData);
}

// 其他API方法的简化实现...
void APIController::handleCreateTask(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) { sendSuccessResponse(request, "Task creation not fully implemented"); }
void APIController::handleCreateTimer(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) { sendSuccessResponse(request, "Timer creation not fully implemented"); }
void APIController::handleGetTimer(AsyncWebServerRequest* request) { sendSuccessResponse(request, "Get timer not fully implemented"); }
void APIController::handleUpdateTimer(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) { sendSuccessResponse(request, "Timer update not fully implemented"); }
void APIController::handleDeleteTimer(AsyncWebServerRequest* request) { sendSuccessResponse(request, "Timer deletion not fully implemented"); }
void APIController::handleToggleTimer(AsyncWebServerRequest* request) { sendSuccessResponse(request, "Timer toggle not fully implemented"); }
void APIController::handleGetSystemPerformance(AsyncWebServerRequest* request) { sendSuccessResponse(request, "System performance not fully implemented"); }
void APIController::handleGetHardwareInfo(AsyncWebServerRequest* request) { sendSuccessResponse(request, "Hardware info not fully implemented"); }
void APIController::handleSystemReset(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) { sendSuccessResponse(request, "System reset not fully implemented"); }
void APIController::handleGetSystemLogs(AsyncWebServerRequest* request) { sendSuccessResponse(request, "System logs not fully implemented"); }
void APIController::handleSaveSystemLogs(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) { sendSuccessResponse(request, "Save logs not fully implemented"); }
void APIController::handleGetConfig(AsyncWebServerRequest* request) { sendSuccessResponse(request, "Get config not fully implemented"); }
void APIController::handleUpdateConfig(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) { sendSuccessResponse(request, "Update config not fully implemented"); }
void APIController::handleExportConfig(AsyncWebServerRequest* request) { sendSuccessResponse(request, "Export config not fully implemented"); }
void APIController::handleImportConfig(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) { sendSuccessResponse(request, "Import config not fully implemented"); }
void APIController::handleResetConfig(AsyncWebServerRequest* request) { sendSuccessResponse(request, "Reset config not fully implemented"); }
void APIController::handleGetOTAStatus(AsyncWebServerRequest* request) { sendSuccessResponse(request, "OTA status not fully implemented"); }
void APIController::handleOTALogin(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) { sendSuccessResponse(request, "OTA login not fully implemented"); }
void APIController::handleOTAFirmware(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) { sendSuccessResponse(request, "OTA firmware not fully implemented"); }
void APIController::handleOTAFilesystem(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) { sendSuccessResponse(request, "OTA filesystem not fully implemented"); }
void APIController::handleBatchSendSignals(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) { sendSuccessResponse(request, "Batch send not fully implemented"); }
void APIController::handleBatchDeleteSignals(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) { sendSuccessResponse(request, "Batch delete not fully implemented"); }
void APIController::handleSaveLearning(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) { sendSuccessResponse(request, "Save learning not fully implemented"); }
void APIController::handleImportSignals(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) { sendSuccessResponse(request, "Import signals not fully implemented"); }
void APIController::handleExportSignals(AsyncWebServerRequest* request) { sendSuccessResponse(request, "Export signals not fully implemented"); }
void APIController::handleExportSelectedSignals(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) { sendSuccessResponse(request, "Export selected not fully implemented"); }
void APIController::handleSearchSignals(AsyncWebServerRequest* request) { sendSuccessResponse(request, "Search signals not fully implemented"); }
void APIController::handleGetSignalStats(AsyncWebServerRequest* request) { sendSuccessResponse(request, "Signal stats not fully implemented"); }
void APIController::handleGetControllerStatus(AsyncWebServerRequest* request) { sendSuccessResponse(request, "Controller status not fully implemented"); }
