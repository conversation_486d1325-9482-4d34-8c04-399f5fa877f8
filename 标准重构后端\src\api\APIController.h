#pragma once

#include "../core/DataStructures.h"
#include "../services/SignalService.h"
#include "../services/TaskService.h"
#include "../services/TimerService.h"
#include "../services/SystemService.h"
#include "../network/WSManager.h"
#include <ESPAsyncWebServer.h>
#include <functional>
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - API控制器
 * 
 * 功能：
 * 1. HTTP API路由管理
 * 2. 请求参数验证和解析
 * 3. 响应格式化和错误处理
 * 4. 跨域支持和安全控制
 * 5. API文档和版本管理
 * 
 * 设计原则：
 * - RESTful设计：标准的REST API设计
 * - 统一响应：统一的响应格式和错误处理
 * - 参数验证：完整的请求参数验证
 * - 安全控制：API访问控制和限流
 * - 性能优化：响应缓存和异步处理
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 */

class APIController {
public:
    // API响应结构
    struct APIResponse {
        bool success;                  // 是否成功
        String message;                // 响应消息
        JsonObject data;               // 响应数据
        ErrorCode errorCode;           // 错误代码
        Timestamp timestamp;           // 时间戳
        
        APIResponse(JsonDocument& doc) : success(false), data(doc.createNestedObject("data")),
                                        errorCode(ErrorCode::SUCCESS), timestamp(millis()) {}
    };
    
    // API配置结构
    struct APIConfig {
        bool enableCORS;               // 是否启用跨域
        String allowedOrigins;         // 允许的源
        bool enableRateLimit;          // 是否启用限流
        uint32_t rateLimit;            // 限流次数（每分钟）
        bool enableAuthentication;     // 是否启用认证
        String apiKey;                 // API密钥
        uint32_t requestTimeout;       // 请求超时时间
        
        APIConfig() : enableCORS(true), allowedOrigins("*"), enableRateLimit(false),
                     rateLimit(60), enableAuthentication(false), requestTimeout(30000) {}
    };
    
    // 请求统计结构
    struct RequestStats {
        uint32_t totalRequests;        // 总请求数
        uint32_t successfulRequests;   // 成功请求数
        uint32_t failedRequests;       // 失败请求数
        uint32_t authenticationFailures; // 认证失败数
        uint32_t rateLimitExceeded;    // 限流超出次数
        Timestamp lastRequestTime;     // 最后请求时间
        
        RequestStats() : totalRequests(0), successfulRequests(0), failedRequests(0),
                        authenticationFailures(0), rateLimitExceeded(0), lastRequestTime(0) {}
    };

private:
    // 依赖组件
    AsyncWebServer* m_webServer;
    SignalService* m_signalService;
    TaskService* m_taskService;
    TimerService* m_timerService;
    SystemService* m_systemService;
    WSManager* m_wsManager;
    
    // 配置
    APIConfig m_apiConfig;
    
    // 状态
    bool m_initialized;
    
    // 统计信息
    RequestStats m_requestStats;
    
    // 限流管理
    std::map<String, std::vector<Timestamp>> m_rateLimitMap;
    
    // 线程安全
    mutable std::mutex m_mutex;

public:
    /**
     * 构造函数
     * @param webServer Web服务器
     * @param signalService 信号服务
     * @param taskService 任务服务
     * @param timerService 定时器服务
     * @param systemService 系统服务
     * @param wsManager WebSocket管理器
     */
    APIController(AsyncWebServer* webServer, SignalService* signalService,
                  TaskService* taskService, TimerService* timerService,
                  SystemService* systemService, WSManager* wsManager);
    
    /**
     * 析构函数
     */
    ~APIController();
    
    // ==================== 生命周期管理 ====================
    
    /**
     * 初始化API控制器
     * @return 是否成功
     */
    bool initialize();
    
    /**
     * 清理资源
     */
    void cleanup();
    
    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return m_initialized; }
    
    // ==================== 路由注册 ====================
    
    /**
     * 注册所有API路由
     */
    void registerRoutes();
    
    /**
     * 注册信号管理API
     */
    void registerSignalRoutes();
    
    /**
     * 注册任务管理API
     */
    void registerTaskRoutes();
    
    /**
     * 注册定时器管理API
     */
    void registerTimerRoutes();
    
    /**
     * 注册系统管理API
     */
    void registerSystemRoutes();
    
    /**
     * 注册配置管理API
     */
    void registerConfigRoutes();
    
    /**
     * 注册OTA管理API
     */
    void registerOTARoutes();
    
    // ==================== 信号管理API ====================
    
    /**
     * GET /api/signals - 获取所有信号
     */
    void handleGetSignals(AsyncWebServerRequest* request);
    
    /**
     * POST /api/signals - 创建新信号
     */
    void handleCreateSignal(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    /**
     * GET /api/signals/{id} - 获取特定信号
     */
    void handleGetSignal(AsyncWebServerRequest* request);
    
    /**
     * PUT /api/signals/{id} - 更新信号
     */
    void handleUpdateSignal(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    /**
     * DELETE /api/signals/{id} - 删除信号
     */
    void handleDeleteSignal(AsyncWebServerRequest* request);
    
    /**
     * POST /api/signals/send - 发送信号
     */
    void handleSendSignal(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    /**
     * POST /api/signals/{id}/send - 发送特定信号
     */
    void handleSendSpecificSignal(AsyncWebServerRequest* request);
    
    /**
     * POST /api/signals/batch-send - 批量发送信号
     */
    void handleBatchSendSignals(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    /**
     * POST /api/signals/batch-delete - 批量删除信号
     */
    void handleBatchDeleteSignals(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    /**
     * POST /api/signals/learn/start - 开始学习信号
     */
    void handleStartLearning(AsyncWebServerRequest* request);
    
    /**
     * POST /api/signals/learn/stop - 停止学习信号
     */
    void handleStopLearning(AsyncWebServerRequest* request);
    
    /**
     * POST /api/signals/learn/save - 保存学习的信号
     */
    void handleSaveLearning(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    /**
     * GET /api/signals/learn/status - 获取学习状态
     */
    void handleGetLearningStatus(AsyncWebServerRequest* request);
    
    /**
     * POST /api/signals/import - 导入信号
     */
    void handleImportSignals(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    /**
     * GET /api/signals/export - 导出信号
     */
    void handleExportSignals(AsyncWebServerRequest* request);
    
    /**
     * POST /api/signals/export/selected - 导出选中信号
     */
    void handleExportSelectedSignals(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    /**
     * GET /api/signals/search - 搜索信号
     */
    void handleSearchSignals(AsyncWebServerRequest* request);
    
    /**
     * GET /api/signals/stats - 获取信号统计
     */
    void handleGetSignalStats(AsyncWebServerRequest* request);
    
    /**
     * GET /api/signals/controller/status - 获取控制器状态
     */
    void handleGetControllerStatus(AsyncWebServerRequest* request);
    
    // ==================== 任务管理API ====================
    
    /**
     * GET /api/tasks - 获取所有任务
     */
    void handleGetTasks(AsyncWebServerRequest* request);
    
    /**
     * POST /api/tasks - 创建新任务
     */
    void handleCreateTask(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    // ==================== 定时器管理API ====================
    
    /**
     * GET /api/timers - 获取所有定时器
     */
    void handleGetTimers(AsyncWebServerRequest* request);
    
    /**
     * POST /api/timers - 创建新定时器
     */
    void handleCreateTimer(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    /**
     * GET /api/timers/{id} - 获取特定定时器
     */
    void handleGetTimer(AsyncWebServerRequest* request);
    
    /**
     * PUT /api/timers/{id} - 更新定时器
     */
    void handleUpdateTimer(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    /**
     * DELETE /api/timers/{id} - 删除定时器
     */
    void handleDeleteTimer(AsyncWebServerRequest* request);
    
    /**
     * POST /api/timers/{id}/toggle - 切换定时器状态
     */
    void handleToggleTimer(AsyncWebServerRequest* request);
    
    // ==================== 系统管理API ====================
    
    /**
     * GET /api/system/status - 获取系统状态
     */
    void handleGetSystemStatus(AsyncWebServerRequest* request);
    
    /**
     * GET /api/system/performance - 获取系统性能
     */
    void handleGetSystemPerformance(AsyncWebServerRequest* request);
    
    /**
     * GET /api/system/hardware - 获取硬件信息
     */
    void handleGetHardwareInfo(AsyncWebServerRequest* request);
    
    /**
     * POST /api/system/reset - 系统重置
     */
    void handleSystemReset(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    
    /**
     * GET /api/system/logs - 获取系统日志
     */
    void handleGetSystemLogs(AsyncWebServerRequest* request);
    
    /**
     * POST /api/system/logs - 保存系统日志
     */
    void handleSaveSystemLogs(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);

    // ==================== 配置管理API ====================

    /**
     * GET /api/config - 获取配置
     */
    void handleGetConfig(AsyncWebServerRequest* request);

    /**
     * POST /api/config - 更新配置
     */
    void handleUpdateConfig(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);

    /**
     * GET /api/config/export - 导出配置
     */
    void handleExportConfig(AsyncWebServerRequest* request);

    /**
     * POST /api/config/import - 导入配置
     */
    void handleImportConfig(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);

    /**
     * POST /api/config/reset - 重置配置
     */
    void handleResetConfig(AsyncWebServerRequest* request);

    // ==================== OTA管理API ====================

    /**
     * GET /api/ota/status - 获取OTA状态
     */
    void handleGetOTAStatus(AsyncWebServerRequest* request);

    /**
     * POST /api/ota/login - OTA登录
     */
    void handleOTALogin(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);

    /**
     * POST /api/ota/firmware - 固件更新
     */
    void handleOTAFirmware(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);

    /**
     * POST /api/ota/filesystem - 文件系统更新
     */
    void handleOTAFilesystem(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);

    // ==================== 工具方法 ====================

    /**
     * 创建成功响应
     * @param request 请求对象
     * @param message 响应消息
     * @param data 响应数据（可选）
     */
    void sendSuccessResponse(AsyncWebServerRequest* request, const String& message, JsonObject* data = nullptr);

    /**
     * 创建错误响应
     * @param request 请求对象
     * @param errorCode 错误代码
     * @param message 错误消息
     * @param httpCode HTTP状态码
     */
    void sendErrorResponse(AsyncWebServerRequest* request, ErrorCode errorCode, const String& message, int httpCode = 400);

    /**
     * 验证请求参数
     * @param request 请求对象
     * @param requiredParams 必需参数列表
     * @return 是否验证通过
     */
    bool validateRequestParams(AsyncWebServerRequest* request, const std::vector<String>& requiredParams);

    /**
     * 解析JSON请求体
     * @param data 请求数据
     * @param len 数据长度
     * @param doc JSON文档
     * @return 是否解析成功
     */
    bool parseJSONBody(uint8_t* data, size_t len, JsonDocument& doc);

    /**
     * 检查API认证
     * @param request 请求对象
     * @return 是否认证通过
     */
    bool checkAuthentication(AsyncWebServerRequest* request);

    /**
     * 检查限流
     * @param clientIP 客户端IP
     * @return 是否通过限流检查
     */
    bool checkRateLimit(const String& clientIP);

    /**
     * 添加CORS头
     * @param response 响应对象
     */
    void addCORSHeaders(AsyncWebServerResponse* response);

    /**
     * 记录API请求
     * @param request 请求对象
     * @param success 是否成功
     * @param errorCode 错误代码（如果失败）
     */
    void logAPIRequest(AsyncWebServerRequest* request, bool success, ErrorCode errorCode = ErrorCode::SUCCESS);

    /**
     * 获取客户端IP
     * @param request 请求对象
     * @return 客户端IP
     */
    String getClientIP(AsyncWebServerRequest* request);

    /**
     * 获取请求统计信息
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getRequestStatistics(JsonDocument& doc);

    /**
     * 重置请求统计
     */
    void resetRequestStatistics();

    /**
     * 更新API配置
     * @param config 新配置
     */
    void updateAPIConfig(const APIConfig& config);

    /**
     * 获取API配置
     * @return API配置
     */
    const APIConfig& getAPIConfig() const { return m_apiConfig; }

private:
    // ==================== 内部实现方法 ====================

    /**
     * 处理OPTIONS请求（CORS预检）
     * @param request 请求对象
     */
    void handleOptionsRequest(AsyncWebServerRequest* request);

    /**
     * 处理404错误
     * @param request 请求对象
     */
    void handleNotFound(AsyncWebServerRequest* request);

    /**
     * 清理过期的限流记录
     */
    void cleanupRateLimitMap();

    /**
     * 验证信号ID参数
     * @param request 请求对象
     * @return 信号ID，如果无效返回INVALID_ID
     */
    SignalID validateSignalID(AsyncWebServerRequest* request);

    /**
     * 验证任务ID参数
     * @param request 请求对象
     * @return 任务ID，如果无效返回INVALID_ID
     */
    TaskID validateTaskID(AsyncWebServerRequest* request);

    /**
     * 验证定时器ID参数
     * @param request 请求对象
     * @return 定时器ID，如果无效返回INVALID_ID
     */
    TimerID validateTimerID(AsyncWebServerRequest* request);
};
