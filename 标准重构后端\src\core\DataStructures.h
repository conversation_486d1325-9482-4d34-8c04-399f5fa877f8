#pragma once

#include <Arduino.h>
#include <vector>
#include <cstdint>
#include <ArduinoJson.h>

/**
 * ESP32-S3 红外控制系统 - 统一数据结构定义
 * 
 * 设计原则：
 * 1. 统一ID系统 - 所有实体使用EntityID
 * 2. 内存优化 - 使用位域和紧凑结构
 * 3. JSON兼容 - 支持前后端通信
 * 4. 版本兼容 - 支持数据结构演进
 * 5. 类型安全 - 强类型定义避免错误
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 */

// ==================== 基础类型定义 ====================

// 统一ID类型
using EntityID = uint32_t;
using SignalID = EntityID;
using TaskID = EntityID;
using TimerID = EntityID;
using SessionID = EntityID;
using ConfigID = EntityID;

// 时间戳类型
using Timestamp = uint32_t;

// 特殊ID值
constexpr EntityID INVALID_ID = 0;
constexpr EntityID RESERVED_ID_START = 1;
constexpr EntityID RESERVED_ID_END = 100;
constexpr EntityID USER_ID_START = 101;

// ==================== 枚举类型定义 ====================

// 红外协议类型
enum class IRProtocol : uint8_t {
    UNKNOWN = 0, NEC = 1, SONY = 2, LG = 3, SAMSUNG = 4,
    PANASONIC = 5, JVC = 6, RC5 = 7, RC6 = 8, RAW = 255
};

// 设备类型
enum class DeviceType : uint8_t {
    UNKNOWN = 0, TV = 1, AC = 2, FAN = 3, LIGHT = 4,
    AUDIO = 5, PROJECTOR = 6, OTHER = 255
};

// 任务状态
enum class TaskStatus : uint8_t {
    PENDING = 0, RUNNING = 1, COMPLETED = 2,
    FAILED = 3, CANCELLED = 4, PAUSED = 5
};

// 任务类型
enum class TaskType : uint8_t {
    SINGLE_SIGNAL = 0, BATCH_SIGNALS = 1, SCHEDULED = 2,
    REPEATED = 3, CONDITIONAL = 4
};

// 优先级
enum class Priority : uint8_t {
    LOW = 0, NORMAL = 1, HIGH = 2, CRITICAL = 3
};

// 错误级别
enum class ErrorLevel : uint8_t {
    INFO = 0, WARNING = 1, ERROR = 2, CRITICAL = 3
};

// 网络模式
enum class NetworkMode : uint8_t {
    WIFI_CLIENT = 0, WIFI_AP = 1, ETHERNET = 2
};

// 配置类型
enum class ConfigType : uint8_t {
    SYSTEM = 0, NETWORK = 1, INFRARED = 2, 
    OTA = 3, STORAGE = 4, SECURITY = 5
};

// 系统状态
enum class SystemState : uint8_t {
    INITIALIZING = 0, RUNNING = 1, ERROR = 2,
    MAINTENANCE = 3, SHUTDOWN = 4
};

// 操作结果
enum class OperationResult : uint8_t {
    SUCCESS = 0, FAILED = 1, TIMEOUT = 2,
    INVALID_PARAM = 3, RESOURCE_BUSY = 4
};

// ==================== 错误码定义 ====================

enum class ErrorCode : uint16_t {
    SUCCESS = 0,
    
    // 通用错误 (1000-1099)
    INVALID_PARAMETER = 1001,
    RESOURCE_NOT_FOUND = 1002,
    OPERATION_TIMEOUT = 1003,
    INSUFFICIENT_MEMORY = 1004,
    
    // 信号相关错误 (2000-2099)
    SIGNAL_NOT_FOUND = 2001,
    SIGNAL_INVALID_FORMAT = 2002,
    SIGNAL_LEARNING_FAILED = 2003,
    SIGNAL_SEND_FAILED = 2004,
    
    // 任务相关错误 (3000-3099)
    TASK_NOT_FOUND = 3001,
    TASK_EXECUTION_FAILED = 3002,
    TASK_INVALID_SCHEDULE = 3003,
    
    // 系统相关错误 (4000-4099)
    SYSTEM_INITIALIZATION_FAILED = 4001,
    SYSTEM_HARDWARE_ERROR = 4002,
    SYSTEM_NETWORK_ERROR = 4003,
    
    // 存储相关错误 (5000-5099)
    STORAGE_READ_FAILED = 5001,
    STORAGE_WRITE_FAILED = 5002,
    STORAGE_CORRUPTION = 5003
};

// ==================== 核心数据结构 ====================

/**
 * 信号数据结构
 */
struct SignalData {
    SignalID id;                    // 信号ID
    String name;                    // 信号名称
    String description;             // 信号描述
    IRProtocol protocol;            // 红外协议
    DeviceType deviceType;          // 设备类型
    
    // 信号数据
    uint64_t code;                  // 信号代码
    uint8_t bits;                   // 数据位数
    uint16_t frequency;             // 载波频率
    std::vector<uint16_t> rawData;  // 原始数据
    
    // 元数据
    Timestamp createdTime;          // 创建时间
    Timestamp modifiedTime;         // 修改时间
    uint32_t usageCount;            // 使用次数
    Timestamp lastUsed;             // 最后使用时间
    
    // 构造函数
    SignalData() : id(INVALID_ID), protocol(IRProtocol::UNKNOWN),
                   deviceType(DeviceType::UNKNOWN), code(0), bits(0),
                   frequency(38000), createdTime(millis()),
                   modifiedTime(millis()), usageCount(0), lastUsed(0) {}
    
    // 方法声明
    bool isValid() const;
    void updateUsage();
    JsonObject toJson(JsonDocument& doc) const;
    static SignalData fromJson(const JsonObject& json);
};

/**
 * 任务数据结构
 */
struct TaskData {
    TaskID id;                      // 任务ID
    String name;                    // 任务名称
    String description;             // 任务描述
    TaskStatus status;              // 任务状态
    TaskType type;                  // 任务类型
    Priority priority;              // 优先级
    
    // 任务配置
    std::vector<SignalID> signals;  // 信号列表
    uint32_t interval;              // 执行间隔
    uint32_t repeatCount;           // 重复次数
    bool enabled;                   // 是否启用
    
    // 时间信息
    Timestamp createdTime;          // 创建时间
    Timestamp modifiedTime;         // 修改时间
    Timestamp lastExecuted;         // 最后执行时间
    Timestamp nextExecution;        // 下次执行时间
    
    // 统计信息
    uint32_t executionCount;        // 执行次数
    uint32_t successCount;          // 成功次数
    uint32_t failureCount;          // 失败次数
    
    // 构造函数
    TaskData() : id(INVALID_ID), status(TaskStatus::PENDING),
                 type(TaskType::SINGLE_SIGNAL), priority(Priority::NORMAL),
                 interval(0), repeatCount(1), enabled(true),
                 createdTime(millis()), modifiedTime(millis()),
                 lastExecuted(0), nextExecution(0),
                 executionCount(0), successCount(0), failureCount(0) {}
    
    // 方法声明
    bool isValid() const;
    bool shouldExecute() const;
    JsonObject toJson(JsonDocument& doc) const;
    static TaskData fromJson(const JsonObject& json);
};

/**
 * 定时器数据结构
 */
struct TimerData {
    TimerID id;                     // 定时器ID
    String name;                    // 定时器名称
    TaskID taskId;                  // 关联任务ID
    
    // 定时配置
    uint8_t hour;                   // 小时 (0-23)
    uint8_t minute;                 // 分钟 (0-59)
    uint8_t weekdays;               // 星期掩码 (bit0=周日)
    bool enabled;                   // 是否启用
    bool oneTime;                   // 是否一次性
    
    // 时间信息
    Timestamp createdTime;          // 创建时间
    Timestamp modifiedTime;         // 修改时间
    Timestamp lastTriggered;        // 最后触发时间
    Timestamp nextTrigger;          // 下次触发时间
    
    // 构造函数
    TimerData() : id(INVALID_ID), taskId(INVALID_ID), hour(0), minute(0),
                  weekdays(0), enabled(true), oneTime(false),
                  createdTime(millis()), modifiedTime(millis()),
                  lastTriggered(0), nextTrigger(0) {}
    
    // 方法声明
    bool isValid() const;
    bool shouldTrigger() const;
    Timestamp calculateNextTrigger() const;
    JsonObject toJson(JsonDocument& doc) const;
    static TimerData fromJson(const JsonObject& json);
};

/**
 * 操作结果结构
 */
struct OperationResultData {
    bool success;                   // 操作是否成功
    String message;                 // 结果消息
    ErrorCode errorCode;            // 错误代码
    Timestamp timestamp;            // 时间戳
    
    // 构造函数
    OperationResultData() : success(false), errorCode(ErrorCode::SUCCESS), 
                           timestamp(millis()) {}
    
    OperationResultData(bool s, const String& msg, ErrorCode code = ErrorCode::SUCCESS)
        : success(s), message(msg), errorCode(code), timestamp(millis()) {}
    
    // 静态工厂方法
    static OperationResultData createSuccess(const String& message = "Operation successful");
    static OperationResultData createError(ErrorCode code, const String& message);
    
    // 方法声明
    JsonObject toJson(JsonDocument& doc) const;
};

// ==================== 结果类型模板 ====================

/**
 * 结果类型模板 - 用于函数返回值
 */
template<typename T>
class Result {
private:
    bool m_success;
    T m_value;
    ErrorCode m_errorCode;
    String m_errorMessage;

public:
    // 构造函数
    Result(bool success, const T& value, ErrorCode code, const String& message)
        : m_success(success), m_value(value), m_errorCode(code), m_errorMessage(message) {}
    
    // 静态工厂方法
    static Result<T> Success(const T& value) {
        return Result<T>(true, value, ErrorCode::SUCCESS, "");
    }
    
    static Result<T> Error(ErrorCode code, const String& message) {
        return Result<T>(false, T{}, code, message);
    }
    
    // 访问方法
    bool isSuccess() const { return m_success; }
    const T& getValue() const { return m_value; }
    ErrorCode getErrorCode() const { return m_errorCode; }
    const String& getErrorMessage() const { return m_errorMessage; }
    
    // 操作符重载
    explicit operator bool() const { return m_success; }
};
