#include "IDGenerator.h"
#include <Arduino.h>

// 静态成员初始化
IDGenerator* IDGenerator::s_instance = nullptr;
std::mutex IDGenerator::s_mutex;

// 常量定义
static const char* NVS_NAMESPACE = "id_generator";
static const char* SIGNAL_COUNTER_KEY = "signal_cnt";
static const char* TASK_COUNTER_KEY = "task_cnt";
static const char* TIMER_COUNTER_KEY = "timer_cnt";
static const char* SESSION_COUNTER_KEY = "session_cnt";
static const char* CONFIG_COUNTER_KEY = "config_cnt";

IDGenerator::IDGenerator() : m_initialized(false) {
    // 初始化计数器
    m_counters["signal"] = USER_ID_START;
    m_counters["task"] = USER_ID_START;
    m_counters["timer"] = USER_ID_START;
    m_counters["session"] = USER_ID_START;
    m_counters["config"] = USER_ID_START;
}

IDGenerator& IDGenerator::getInstance() {
    std::lock_guard<std::mutex> lock(s_mutex);
    if (s_instance == nullptr) {
        s_instance = new IDGenerator();
    }
    return *s_instance;
}

void IDGenerator::destroyInstance() {
    std::lock_guard<std::mutex> lock(s_mutex);
    if (s_instance != nullptr) {
        s_instance->cleanup();
        delete s_instance;
        s_instance = nullptr;
    }
}

bool IDGenerator::initialize() {
    std::lock_guard<std::mutex> lock(m_counterMutex);
    
    if (m_initialized) {
        return true;
    }
    
    // 初始化NVS
    if (!m_preferences.begin(NVS_NAMESPACE, false)) {
        Serial.println("❌ IDGenerator: Failed to initialize NVS");
        return false;
    }
    
    // 加载计数器
    if (!loadCounters()) {
        Serial.println("⚠️ IDGenerator: Failed to load counters, using defaults");
    }
    
    m_initialized = true;
    Serial.println("✅ IDGenerator: Initialized successfully");
    
    return true;
}

void IDGenerator::cleanup() {
    std::lock_guard<std::mutex> lock(m_counterMutex);
    
    if (m_initialized) {
        // 保存计数器
        saveCounters();
        
        // 关闭NVS
        m_preferences.end();
        
        m_initialized = false;
        Serial.println("✅ IDGenerator: Cleanup completed");
    }
}

SignalID IDGenerator::generateSignalID() {
    return getNextID("signal");
}

TaskID IDGenerator::generateTaskID() {
    return getNextID("task");
}

TimerID IDGenerator::generateTimerID() {
    return getNextID("timer");
}

SessionID IDGenerator::generateSessionID() {
    return getNextID("session");
}

ConfigID IDGenerator::generateConfigID() {
    return getNextID("config");
}

EntityID IDGenerator::generateID(const String& type) {
    return getNextID(type);
}

EntityID IDGenerator::getCurrentCounter(const String& type) const {
    std::lock_guard<std::mutex> lock(m_counterMutex);
    
    auto it = m_counters.find(type);
    if (it != m_counters.end()) {
        return it->second;
    }
    
    return USER_ID_START;
}

bool IDGenerator::setCounter(const String& type, EntityID value) {
    std::lock_guard<std::mutex> lock(m_counterMutex);
    
    if (!isValidCounter(value)) {
        Serial.printf("❌ IDGenerator: Invalid counter value %u for type %s\n", 
                     value, type.c_str());
        return false;
    }
    
    m_counters[type] = value;
    
    // 立即保存到NVS
    if (m_initialized) {
        return saveCounter(type, value);
    }
    
    return true;
}

bool IDGenerator::resetAllCounters() {
    std::lock_guard<std::mutex> lock(m_counterMutex);
    
    // 重置所有计数器
    for (auto& pair : m_counters) {
        pair.second = USER_ID_START;
    }
    
    // 保存到NVS
    if (m_initialized) {
        return saveCounters();
    }
    
    Serial.println("✅ IDGenerator: All counters reset");
    return true;
}

JsonObject IDGenerator::getStatistics(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_counterMutex);
    
    JsonObject stats = doc.createNestedObject("id_generator");
    stats["initialized"] = m_initialized;
    stats["total_types"] = m_counters.size();
    
    JsonObject counters = stats.createNestedObject("counters");
    for (const auto& pair : m_counters) {
        counters[pair.first] = pair.second;
    }
    
    return stats;
}

bool IDGenerator::saveCounters() {
    if (!m_initialized) {
        return false;
    }
    
    bool success = true;
    
    // 保存各类型计数器
    success &= saveCounter("signal", m_counters["signal"]);
    success &= saveCounter("task", m_counters["task"]);
    success &= saveCounter("timer", m_counters["timer"]);
    success &= saveCounter("session", m_counters["session"]);
    success &= saveCounter("config", m_counters["config"]);
    
    if (success) {
        Serial.println("✅ IDGenerator: Counters saved to NVS");
    } else {
        Serial.println("❌ IDGenerator: Failed to save some counters");
    }
    
    return success;
}

bool IDGenerator::loadCounters() {
    if (!m_initialized) {
        return false;
    }
    
    // 加载各类型计数器
    m_counters["signal"] = loadCounter("signal");
    m_counters["task"] = loadCounter("task");
    m_counters["timer"] = loadCounter("timer");
    m_counters["session"] = loadCounter("session");
    m_counters["config"] = loadCounter("config");
    
    Serial.println("✅ IDGenerator: Counters loaded from NVS");
    return true;
}

EntityID IDGenerator::getNextID(const String& type) {
    std::lock_guard<std::mutex> lock(m_counterMutex);
    
    // 获取当前计数器
    EntityID current = m_counters[type];
    
    // 检查溢出
    if (current >= UINT32_MAX - 1) {
        Serial.printf("❌ IDGenerator: Counter overflow for type %s\n", type.c_str());
        return INVALID_ID;
    }
    
    // 递增计数器
    EntityID newID = ++m_counters[type];
    
    // 异步保存到NVS（避免阻塞）
    if (m_initialized && (newID % 100 == 0)) { // 每100个ID保存一次
        saveCounter(type, newID);
    }
    
    return newID;
}

bool IDGenerator::saveCounter(const String& type, EntityID value) {
    if (!m_initialized) {
        return false;
    }
    
    const char* key = nullptr;
    
    if (type == "signal") {
        key = SIGNAL_COUNTER_KEY;
    } else if (type == "task") {
        key = TASK_COUNTER_KEY;
    } else if (type == "timer") {
        key = TIMER_COUNTER_KEY;
    } else if (type == "session") {
        key = SESSION_COUNTER_KEY;
    } else if (type == "config") {
        key = CONFIG_COUNTER_KEY;
    } else {
        // 动态类型，使用类型名作为key
        key = type.c_str();
    }
    
    size_t written = m_preferences.putUInt(key, value);
    return written > 0;
}

EntityID IDGenerator::loadCounter(const String& type) {
    if (!m_initialized) {
        return USER_ID_START;
    }
    
    const char* key = nullptr;
    
    if (type == "signal") {
        key = SIGNAL_COUNTER_KEY;
    } else if (type == "task") {
        key = TASK_COUNTER_KEY;
    } else if (type == "timer") {
        key = TIMER_COUNTER_KEY;
    } else if (type == "session") {
        key = SESSION_COUNTER_KEY;
    } else if (type == "config") {
        key = CONFIG_COUNTER_KEY;
    } else {
        // 动态类型，使用类型名作为key
        key = type.c_str();
    }
    
    EntityID value = m_preferences.getUInt(key, USER_ID_START);
    
    // 确保加载的值有效
    if (!isValidCounter(value)) {
        Serial.printf("⚠️ IDGenerator: Invalid counter value %u for %s, using default\n", 
                     value, type.c_str());
        return USER_ID_START;
    }
    
    return value;
}

bool IDGenerator::isValidCounter(EntityID value) const {
    return value >= USER_ID_START && value < UINT32_MAX - 1000; // 留出安全边界
}
