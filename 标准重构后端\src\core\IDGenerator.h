#pragma once

#include "DataStructures.h"
#include <Preferences.h>
#include <map>
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - ID生成器
 * 
 * 功能：
 * 1. 统一ID生成和管理
 * 2. 持久化ID计数器
 * 3. 线程安全的ID分配
 * 4. 字符串与ID互转
 * 5. ID范围管理和验证
 * 
 * 设计原则：
 * - 线程安全：使用互斥锁保护
 * - 持久化：ID计数器存储在NVS
 * - 高效：内存缓存避免频繁NVS访问
 * - 安全：ID范围检查和验证
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 */

class IDGenerator {
private:
    // 单例模式
    static IDGenerator* s_instance;
    static std::mutex s_mutex;
    
    // ID计数器
    std::map<String, EntityID> m_counters;
    
    // NVS存储
    Preferences m_preferences;
    bool m_initialized;
    
    // 内部锁
    mutable std::mutex m_counterMutex;
    
    // 私有构造函数
    IDGenerator();
    
    // 禁用拷贝
    IDGenerator(const IDGenerator&) = delete;
    IDGenerator& operator=(const IDGenerator&) = delete;

public:
    /**
     * 获取单例实例
     */
    static IDGenerator& getInstance();
    
    /**
     * 销毁单例实例
     */
    static void destroyInstance();
    
    /**
     * 初始化ID生成器
     * @return 是否成功
     */
    bool initialize();
    
    /**
     * 清理资源
     */
    void cleanup();
    
    /**
     * 生成新的信号ID
     * @return 新的信号ID
     */
    SignalID generateSignalID();
    
    /**
     * 生成新的任务ID
     * @return 新的任务ID
     */
    TaskID generateTaskID();
    
    /**
     * 生成新的定时器ID
     * @return 新的定时器ID
     */
    TimerID generateTimerID();
    
    /**
     * 生成新的会话ID
     * @return 新的会话ID
     */
    SessionID generateSessionID();
    
    /**
     * 生成新的配置ID
     * @return 新的配置ID
     */
    ConfigID generateConfigID();
    
    /**
     * 生成指定类型的ID
     * @param type ID类型名称
     * @return 新的ID
     */
    EntityID generateID(const String& type);
    
    /**
     * 验证ID是否有效
     * @param id 要验证的ID
     * @return 是否有效
     */
    static bool isValidID(EntityID id);
    
    /**
     * 验证ID是否在用户范围内
     * @param id 要验证的ID
     * @return 是否在用户范围
     */
    static bool isUserID(EntityID id);
    
    /**
     * 验证ID是否在保留范围内
     * @param id 要验证的ID
     * @return 是否在保留范围
     */
    static bool isReservedID(EntityID id);
    
    /**
     * 将ID转换为字符串
     * @param id 要转换的ID
     * @return 字符串表示
     */
    static String toString(EntityID id);
    
    /**
     * 从字符串解析ID
     * @param str 字符串表示
     * @return 解析的ID，失败返回INVALID_ID
     */
    static EntityID fromString(const String& str);
    
    /**
     * 获取当前计数器值
     * @param type ID类型名称
     * @return 当前计数器值
     */
    EntityID getCurrentCounter(const String& type) const;
    
    /**
     * 设置计数器值（用于数据恢复）
     * @param type ID类型名称
     * @param value 计数器值
     * @return 是否成功
     */
    bool setCounter(const String& type, EntityID value);
    
    /**
     * 重置所有计数器
     * @return 是否成功
     */
    bool resetAllCounters();
    
    /**
     * 获取统计信息
     * @return JSON格式的统计信息
     */
    JsonObject getStatistics(JsonDocument& doc) const;
    
    /**
     * 保存计数器到NVS
     * @return 是否成功
     */
    bool saveCounters();
    
    /**
     * 从NVS加载计数器
     * @return 是否成功
     */
    bool loadCounters();

private:
    /**
     * 获取下一个ID
     * @param type ID类型名称
     * @return 新的ID
     */
    EntityID getNextID(const String& type);
    
    /**
     * 保存单个计数器
     * @param type ID类型名称
     * @param value 计数器值
     * @return 是否成功
     */
    bool saveCounter(const String& type, EntityID value);
    
    /**
     * 加载单个计数器
     * @param type ID类型名称
     * @return 计数器值
     */
    EntityID loadCounter(const String& type);
    
    /**
     * 验证计数器值
     * @param value 计数器值
     * @return 是否有效
     */
    bool isValidCounter(EntityID value) const;
};

// ==================== 内联函数实现 ====================

inline bool IDGenerator::isValidID(EntityID id) {
    return id != INVALID_ID && id < UINT32_MAX;
}

inline bool IDGenerator::isUserID(EntityID id) {
    return id >= USER_ID_START;
}

inline bool IDGenerator::isReservedID(EntityID id) {
    return id >= RESERVED_ID_START && id <= RESERVED_ID_END;
}

inline String IDGenerator::toString(EntityID id) {
    return String(id);
}

inline EntityID IDGenerator::fromString(const String& str) {
    if (str.isEmpty() || str.length() > 10) {
        return INVALID_ID;
    }
    
    // 验证字符串只包含数字
    for (size_t i = 0; i < str.length(); i++) {
        if (!isDigit(str[i])) {
            return INVALID_ID;
        }
    }
    
    // 转换为数字
    unsigned long value = str.toInt();
    if (value == 0 && str != "0") {
        return INVALID_ID;
    }
    
    if (value > UINT32_MAX) {
        return INVALID_ID;
    }
    
    EntityID id = static_cast<EntityID>(value);
    return isValidID(id) ? id : INVALID_ID;
}

// ==================== 便利宏定义 ====================

#define GENERATE_SIGNAL_ID() IDGenerator::getInstance().generateSignalID()
#define GENERATE_TASK_ID() IDGenerator::getInstance().generateTaskID()
#define GENERATE_TIMER_ID() IDGenerator::getInstance().generateTimerID()
#define GENERATE_SESSION_ID() IDGenerator::getInstance().generateSessionID()
#define GENERATE_CONFIG_ID() IDGenerator::getInstance().generateConfigID()

#define VALIDATE_ID(id) IDGenerator::isValidID(id)
#define ID_TO_STRING(id) IDGenerator::toString(id)
#define STRING_TO_ID(str) IDGenerator::fromString(str)
