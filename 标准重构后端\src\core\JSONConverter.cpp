#include "JSONConverter.h"
#include <Arduino.h>

// ==================== 信号数据转换实现 ====================

JsonObject JSONConverter::signalToJson(const SignalData& signal, JsonDocument& doc) {
    JsonObject obj = doc.createNestedObject();
    
    // 基础信息
    obj["id"] = IDGenerator::toString(signal.id);
    obj["name"] = sanitizeString(signal.name);
    obj["description"] = sanitizeString(signal.description);
    obj["protocol"] = protocolToString(signal.protocol);
    obj["deviceType"] = deviceTypeToString(signal.deviceType);
    
    // 信号数据
    obj["code"] = String(signal.code);  // 转为字符串避免精度丢失
    obj["bits"] = signal.bits;
    obj["frequency"] = signal.frequency;
    
    // 原始数据（如果存在）
    if (!signal.rawData.empty() && signal.rawData.size() <= MAX_ARRAY_SIZE) {
        JsonArray rawArray = obj.createNestedArray("rawData");
        for (uint16_t value : signal.rawData) {
            rawArray.add(value);
        }
    }
    
    // 元数据
    obj["createdTime"] = signal.createdTime;
    obj["modifiedTime"] = signal.modifiedTime;
    obj["usageCount"] = signal.usageCount;
    obj["lastUsed"] = signal.lastUsed;
    
    return obj;
}

Result<SignalData> JSONConverter::signalFromJson(const JsonObject& json) {
    // 验证必需字段
    std::vector<String> requiredFields = {"id", "name", "protocol", "deviceType"};
    if (!validateRequiredFields(json, requiredFields)) {
        return Result<SignalData>::Error(ErrorCode::INVALID_PARAMETER, "Missing required fields");
    }
    
    SignalData signal;
    
    // 解析ID
    String idStr = getStringValue(json, "id");
    signal.id = IDGenerator::fromString(idStr);
    if (signal.id == INVALID_ID) {
        return Result<SignalData>::Error(ErrorCode::INVALID_PARAMETER, "Invalid signal ID");
    }
    
    // 解析基础信息
    signal.name = getStringValue(json, "name", "", 50);
    signal.description = getStringValue(json, "description", "", 200);
    signal.protocol = protocolFromString(getStringValue(json, "protocol"));
    signal.deviceType = deviceTypeFromString(getStringValue(json, "deviceType"));
    
    // 解析信号数据
    String codeStr = getStringValue(json, "code", "0");
    signal.code = strtoull(codeStr.c_str(), nullptr, 10);
    signal.bits = getUIntValue(json, "bits", 32, 1, 64);
    signal.frequency = getUIntValue(json, "frequency", 38000, 30000, 60000);
    
    // 解析原始数据
    if (json.containsKey("rawData") && json["rawData"].is<JsonArray>()) {
        JsonArray rawArray = json["rawData"];
        if (isValidArraySize(rawArray.size())) {
            signal.rawData.clear();
            signal.rawData.reserve(rawArray.size());
            for (JsonVariant value : rawArray) {
                if (value.is<uint16_t>()) {
                    signal.rawData.push_back(value.as<uint16_t>());
                }
            }
        }
    }
    
    // 解析元数据
    signal.createdTime = getUIntValue(json, "createdTime", millis());
    signal.modifiedTime = getUIntValue(json, "modifiedTime", millis());
    signal.usageCount = getUIntValue(json, "usageCount", 0);
    signal.lastUsed = getUIntValue(json, "lastUsed", 0);
    
    // 验证信号数据有效性
    if (!signal.isValid()) {
        return Result<SignalData>::Error(ErrorCode::SIGNAL_INVALID_FORMAT, "Invalid signal data");
    }
    
    return Result<SignalData>::Success(signal);
}

JsonArray JSONConverter::signalsToJsonArray(const std::vector<SignalData>& signals, JsonDocument& doc) {
    JsonArray array = doc.createNestedArray();
    
    for (const auto& signal : signals) {
        JsonObject signalObj = array.createNestedObject();
        // 直接在数组对象中创建，避免临时对象
        signalObj["id"] = IDGenerator::toString(signal.id);
        signalObj["name"] = sanitizeString(signal.name);
        signalObj["description"] = sanitizeString(signal.description);
        signalObj["protocol"] = protocolToString(signal.protocol);
        signalObj["deviceType"] = deviceTypeToString(signal.deviceType);
        signalObj["code"] = String(signal.code);
        signalObj["bits"] = signal.bits;
        signalObj["frequency"] = signal.frequency;
        signalObj["createdTime"] = signal.createdTime;
        signalObj["modifiedTime"] = signal.modifiedTime;
        signalObj["usageCount"] = signal.usageCount;
        signalObj["lastUsed"] = signal.lastUsed;
    }
    
    return array;
}

Result<std::vector<SignalData>> JSONConverter::signalsFromJsonArray(const JsonArray& jsonArray) {
    if (!isValidArraySize(jsonArray.size())) {
        return Result<std::vector<SignalData>>::Error(ErrorCode::INVALID_PARAMETER, "Array size too large");
    }
    
    std::vector<SignalData> signals;
    signals.reserve(jsonArray.size());
    
    for (JsonVariant item : jsonArray) {
        if (item.is<JsonObject>()) {
            auto result = signalFromJson(item.as<JsonObject>());
            if (result.isSuccess()) {
                signals.push_back(result.getValue());
            } else {
                return Result<std::vector<SignalData>>::Error(result.getErrorCode(), result.getErrorMessage());
            }
        }
    }
    
    return Result<std::vector<SignalData>>::Success(signals);
}

// ==================== 任务数据转换实现 ====================

JsonObject JSONConverter::taskToJson(const TaskData& task, JsonDocument& doc) {
    JsonObject obj = doc.createNestedObject();
    
    // 基础信息
    obj["id"] = IDGenerator::toString(task.id);
    obj["name"] = sanitizeString(task.name);
    obj["description"] = sanitizeString(task.description);
    obj["status"] = taskStatusToString(task.status);
    obj["type"] = taskTypeToString(task.type);
    obj["priority"] = priorityToString(task.priority);
    
    // 任务配置
    if (!task.signals.empty() && task.signals.size() <= MAX_ARRAY_SIZE) {
        JsonArray signalsArray = obj.createNestedArray("signals");
        for (SignalID signalId : task.signals) {
            signalsArray.add(IDGenerator::toString(signalId));
        }
    }
    
    obj["interval"] = task.interval;
    obj["repeatCount"] = task.repeatCount;
    obj["enabled"] = task.enabled;
    
    // 时间信息
    obj["createdTime"] = task.createdTime;
    obj["modifiedTime"] = task.modifiedTime;
    obj["lastExecuted"] = task.lastExecuted;
    obj["nextExecution"] = task.nextExecution;
    
    // 统计信息
    obj["executionCount"] = task.executionCount;
    obj["successCount"] = task.successCount;
    obj["failureCount"] = task.failureCount;
    
    return obj;
}

Result<TaskData> JSONConverter::taskFromJson(const JsonObject& json) {
    // 验证必需字段
    std::vector<String> requiredFields = {"id", "name", "status", "type"};
    if (!validateRequiredFields(json, requiredFields)) {
        return Result<TaskData>::Error(ErrorCode::INVALID_PARAMETER, "Missing required fields");
    }
    
    TaskData task;
    
    // 解析ID
    String idStr = getStringValue(json, "id");
    task.id = IDGenerator::fromString(idStr);
    if (task.id == INVALID_ID) {
        return Result<TaskData>::Error(ErrorCode::INVALID_PARAMETER, "Invalid task ID");
    }
    
    // 解析基础信息
    task.name = getStringValue(json, "name", "", 50);
    task.description = getStringValue(json, "description", "", 200);
    task.status = taskStatusFromString(getStringValue(json, "status"));
    task.type = taskTypeFromString(getStringValue(json, "type"));
    task.priority = priorityFromString(getStringValue(json, "priority", "normal"));
    
    // 解析信号列表
    if (json.containsKey("signals") && json["signals"].is<JsonArray>()) {
        JsonArray signalsArray = json["signals"];
        if (isValidArraySize(signalsArray.size())) {
            task.signals.clear();
            task.signals.reserve(signalsArray.size());
            for (JsonVariant item : signalsArray) {
                if (item.is<const char*>()) {
                    SignalID signalId = IDGenerator::fromString(item.as<String>());
                    if (signalId != INVALID_ID) {
                        task.signals.push_back(signalId);
                    }
                }
            }
        }
    }
    
    // 解析任务配置
    task.interval = getUIntValue(json, "interval", 0);
    task.repeatCount = getUIntValue(json, "repeatCount", 1, 1, 10000);
    task.enabled = getBoolValue(json, "enabled", true);
    
    // 解析时间信息
    task.createdTime = getUIntValue(json, "createdTime", millis());
    task.modifiedTime = getUIntValue(json, "modifiedTime", millis());
    task.lastExecuted = getUIntValue(json, "lastExecuted", 0);
    task.nextExecution = getUIntValue(json, "nextExecution", 0);
    
    // 解析统计信息
    task.executionCount = getUIntValue(json, "executionCount", 0);
    task.successCount = getUIntValue(json, "successCount", 0);
    task.failureCount = getUIntValue(json, "failureCount", 0);
    
    // 验证任务数据有效性
    if (!task.isValid()) {
        return Result<TaskData>::Error(ErrorCode::TASK_INVALID_SCHEDULE, "Invalid task data");
    }
    
    return Result<TaskData>::Success(task);
}

JsonArray JSONConverter::tasksToJsonArray(const std::vector<TaskData>& tasks, JsonDocument& doc) {
    JsonArray array = doc.createNestedArray();
    
    for (const auto& task : tasks) {
        JsonObject taskObj = array.createNestedObject();
        // 直接在数组对象中创建，避免临时对象
        taskObj["id"] = IDGenerator::toString(task.id);
        taskObj["name"] = sanitizeString(task.name);
        taskObj["description"] = sanitizeString(task.description);
        taskObj["status"] = taskStatusToString(task.status);
        taskObj["type"] = taskTypeToString(task.type);
        taskObj["priority"] = priorityToString(task.priority);
        taskObj["interval"] = task.interval;
        taskObj["repeatCount"] = task.repeatCount;
        taskObj["enabled"] = task.enabled;
        taskObj["createdTime"] = task.createdTime;
        taskObj["modifiedTime"] = task.modifiedTime;
        taskObj["lastExecuted"] = task.lastExecuted;
        taskObj["nextExecution"] = task.nextExecution;
        taskObj["executionCount"] = task.executionCount;
        taskObj["successCount"] = task.successCount;
        taskObj["failureCount"] = task.failureCount;
    }
    
    return array;
}

// ==================== 定时器数据转换实现 ====================

JsonObject JSONConverter::timerToJson(const TimerData& timer, JsonDocument& doc) {
    JsonObject obj = doc.createNestedObject();
    
    // 基础信息
    obj["id"] = IDGenerator::toString(timer.id);
    obj["name"] = sanitizeString(timer.name);
    obj["taskId"] = IDGenerator::toString(timer.taskId);
    
    // 定时配置
    obj["hour"] = timer.hour;
    obj["minute"] = timer.minute;
    obj["weekdays"] = timer.weekdays;
    obj["enabled"] = timer.enabled;
    obj["oneTime"] = timer.oneTime;
    
    // 时间信息
    obj["createdTime"] = timer.createdTime;
    obj["modifiedTime"] = timer.modifiedTime;
    obj["lastTriggered"] = timer.lastTriggered;
    obj["nextTrigger"] = timer.nextTrigger;
    
    return obj;
}

Result<TimerData> JSONConverter::timerFromJson(const JsonObject& json) {
    // 验证必需字段
    std::vector<String> requiredFields = {"id", "name", "taskId", "hour", "minute"};
    if (!validateRequiredFields(json, requiredFields)) {
        return Result<TimerData>::Error(ErrorCode::INVALID_PARAMETER, "Missing required fields");
    }
    
    TimerData timer;
    
    // 解析ID
    String idStr = getStringValue(json, "id");
    timer.id = IDGenerator::fromString(idStr);
    if (timer.id == INVALID_ID) {
        return Result<TimerData>::Error(ErrorCode::INVALID_PARAMETER, "Invalid timer ID");
    }
    
    // 解析任务ID
    String taskIdStr = getStringValue(json, "taskId");
    timer.taskId = IDGenerator::fromString(taskIdStr);
    if (timer.taskId == INVALID_ID) {
        return Result<TimerData>::Error(ErrorCode::INVALID_PARAMETER, "Invalid task ID");
    }
    
    // 解析基础信息
    timer.name = getStringValue(json, "name", "", 50);
    
    // 解析定时配置
    timer.hour = getUIntValue(json, "hour", 0, 0, 23);
    timer.minute = getUIntValue(json, "minute", 0, 0, 59);
    timer.weekdays = getUIntValue(json, "weekdays", 0, 0, 127); // 7位掩码
    timer.enabled = getBoolValue(json, "enabled", true);
    timer.oneTime = getBoolValue(json, "oneTime", false);
    
    // 解析时间信息
    timer.createdTime = getUIntValue(json, "createdTime", millis());
    timer.modifiedTime = getUIntValue(json, "modifiedTime", millis());
    timer.lastTriggered = getUIntValue(json, "lastTriggered", 0);
    timer.nextTrigger = getUIntValue(json, "nextTrigger", 0);
    
    // 验证定时器数据有效性
    if (!timer.isValid()) {
        return Result<TimerData>::Error(ErrorCode::INVALID_PARAMETER, "Invalid timer data");
    }
    
    return Result<TimerData>::Success(timer);
}

JsonArray JSONConverter::timersToJsonArray(const std::vector<TimerData>& timers, JsonDocument& doc) {
    JsonArray array = doc.createNestedArray();
    
    for (const auto& timer : timers) {
        JsonObject timerObj = array.createNestedObject();
        // 直接在数组对象中创建，避免临时对象
        timerObj["id"] = IDGenerator::toString(timer.id);
        timerObj["name"] = sanitizeString(timer.name);
        timerObj["taskId"] = IDGenerator::toString(timer.taskId);
        timerObj["hour"] = timer.hour;
        timerObj["minute"] = timer.minute;
        timerObj["weekdays"] = timer.weekdays;
        timerObj["enabled"] = timer.enabled;
        timerObj["oneTime"] = timer.oneTime;
        timerObj["createdTime"] = timer.createdTime;
        timerObj["modifiedTime"] = timer.modifiedTime;
        timerObj["lastTriggered"] = timer.lastTriggered;
        timerObj["nextTrigger"] = timer.nextTrigger;
    }

    return array;
}

// ==================== 操作结果转换实现 ====================

JsonObject JSONConverter::operationResultToJson(const OperationResultData& result, JsonDocument& doc) {
    JsonObject obj = doc.createNestedObject();

    obj["success"] = result.success;
    obj["message"] = sanitizeString(result.message);
    obj["errorCode"] = static_cast<uint16_t>(result.errorCode);
    obj["timestamp"] = result.timestamp;

    return obj;
}

JsonObject JSONConverter::createSuccessResponse(const String& message, JsonDocument& doc, const JsonVariant& data) {
    JsonObject response = doc.createNestedObject();

    response["success"] = true;
    response["message"] = sanitizeString(message);
    response["timestamp"] = millis();

    if (!data.isNull()) {
        response["data"] = data;
    }

    return response;
}

JsonObject JSONConverter::createErrorResponse(ErrorCode errorCode, const String& message, JsonDocument& doc) {
    JsonObject response = doc.createNestedObject();

    response["success"] = false;
    response["message"] = sanitizeString(message);
    response["error"] = static_cast<uint16_t>(errorCode);
    response["timestamp"] = millis();

    return response;
}

// ==================== 枚举转换实现 ====================

String JSONConverter::protocolToString(IRProtocol protocol) {
    switch (protocol) {
        case IRProtocol::NEC: return "NEC";
        case IRProtocol::SONY: return "SONY";
        case IRProtocol::LG: return "LG";
        case IRProtocol::SAMSUNG: return "SAMSUNG";
        case IRProtocol::PANASONIC: return "PANASONIC";
        case IRProtocol::JVC: return "JVC";
        case IRProtocol::RC5: return "RC5";
        case IRProtocol::RC6: return "RC6";
        case IRProtocol::RAW: return "RAW";
        default: return "UNKNOWN";
    }
}

IRProtocol JSONConverter::protocolFromString(const String& str) {
    String upper = str;
    upper.toUpperCase();

    if (upper == "NEC") return IRProtocol::NEC;
    if (upper == "SONY") return IRProtocol::SONY;
    if (upper == "LG") return IRProtocol::LG;
    if (upper == "SAMSUNG") return IRProtocol::SAMSUNG;
    if (upper == "PANASONIC") return IRProtocol::PANASONIC;
    if (upper == "JVC") return IRProtocol::JVC;
    if (upper == "RC5") return IRProtocol::RC5;
    if (upper == "RC6") return IRProtocol::RC6;
    if (upper == "RAW") return IRProtocol::RAW;

    return IRProtocol::UNKNOWN;
}

String JSONConverter::deviceTypeToString(DeviceType type) {
    switch (type) {
        case DeviceType::TV: return "TV";
        case DeviceType::AC: return "AC";
        case DeviceType::FAN: return "FAN";
        case DeviceType::LIGHT: return "LIGHT";
        case DeviceType::AUDIO: return "AUDIO";
        case DeviceType::PROJECTOR: return "PROJECTOR";
        case DeviceType::OTHER: return "OTHER";
        default: return "UNKNOWN";
    }
}

DeviceType JSONConverter::deviceTypeFromString(const String& str) {
    String upper = str;
    upper.toUpperCase();

    if (upper == "TV") return DeviceType::TV;
    if (upper == "AC") return DeviceType::AC;
    if (upper == "FAN") return DeviceType::FAN;
    if (upper == "LIGHT") return DeviceType::LIGHT;
    if (upper == "AUDIO") return DeviceType::AUDIO;
    if (upper == "PROJECTOR") return DeviceType::PROJECTOR;
    if (upper == "OTHER") return DeviceType::OTHER;

    return DeviceType::UNKNOWN;
}

String JSONConverter::taskStatusToString(TaskStatus status) {
    switch (status) {
        case TaskStatus::PENDING: return "PENDING";
        case TaskStatus::RUNNING: return "RUNNING";
        case TaskStatus::COMPLETED: return "COMPLETED";
        case TaskStatus::FAILED: return "FAILED";
        case TaskStatus::CANCELLED: return "CANCELLED";
        case TaskStatus::PAUSED: return "PAUSED";
        default: return "PENDING";
    }
}

TaskStatus JSONConverter::taskStatusFromString(const String& str) {
    String upper = str;
    upper.toUpperCase();

    if (upper == "PENDING") return TaskStatus::PENDING;
    if (upper == "RUNNING") return TaskStatus::RUNNING;
    if (upper == "COMPLETED") return TaskStatus::COMPLETED;
    if (upper == "FAILED") return TaskStatus::FAILED;
    if (upper == "CANCELLED") return TaskStatus::CANCELLED;
    if (upper == "PAUSED") return TaskStatus::PAUSED;

    return TaskStatus::PENDING;
}

String JSONConverter::taskTypeToString(TaskType type) {
    switch (type) {
        case TaskType::SINGLE_SIGNAL: return "SINGLE_SIGNAL";
        case TaskType::BATCH_SIGNALS: return "BATCH_SIGNALS";
        case TaskType::SCHEDULED: return "SCHEDULED";
        case TaskType::REPEATED: return "REPEATED";
        case TaskType::CONDITIONAL: return "CONDITIONAL";
        default: return "SINGLE_SIGNAL";
    }
}

TaskType JSONConverter::taskTypeFromString(const String& str) {
    String upper = str;
    upper.toUpperCase();

    if (upper == "SINGLE_SIGNAL") return TaskType::SINGLE_SIGNAL;
    if (upper == "BATCH_SIGNALS") return TaskType::BATCH_SIGNALS;
    if (upper == "SCHEDULED") return TaskType::SCHEDULED;
    if (upper == "REPEATED") return TaskType::REPEATED;
    if (upper == "CONDITIONAL") return TaskType::CONDITIONAL;

    return TaskType::SINGLE_SIGNAL;
}

String JSONConverter::priorityToString(Priority priority) {
    switch (priority) {
        case Priority::LOW: return "LOW";
        case Priority::NORMAL: return "NORMAL";
        case Priority::HIGH: return "HIGH";
        case Priority::CRITICAL: return "CRITICAL";
        default: return "NORMAL";
    }
}

Priority JSONConverter::priorityFromString(const String& str) {
    String upper = str;
    upper.toUpperCase();

    if (upper == "LOW") return Priority::LOW;
    if (upper == "NORMAL") return Priority::NORMAL;
    if (upper == "HIGH") return Priority::HIGH;
    if (upper == "CRITICAL") return Priority::CRITICAL;

    return Priority::NORMAL;
}

// ==================== 验证和工具函数实现 ====================

bool JSONConverter::validateRequiredFields(const JsonObject& json, const std::vector<String>& requiredFields) {
    for (const String& field : requiredFields) {
        if (!json.containsKey(field.c_str())) {
            Serial.printf("❌ JSONConverter: Missing required field: %s\n", field.c_str());
            return false;
        }
    }
    return true;
}

String JSONConverter::getStringValue(const JsonObject& json, const char* key, const String& defaultValue, size_t maxLength) {
    if (!json.containsKey(key)) {
        return defaultValue;
    }

    String value = json[key].as<String>();
    return sanitizeString(value, maxLength);
}

uint32_t JSONConverter::getUIntValue(const JsonObject& json, const char* key, uint32_t defaultValue, uint32_t minValue, uint32_t maxValue) {
    if (!json.containsKey(key)) {
        return defaultValue;
    }

    uint32_t value = json[key].as<uint32_t>();

    // 边界检查
    if (value < minValue) {
        return minValue;
    }
    if (value > maxValue) {
        return maxValue;
    }

    return value;
}

bool JSONConverter::getBoolValue(const JsonObject& json, const char* key, bool defaultValue) {
    if (!json.containsKey(key)) {
        return defaultValue;
    }

    return json[key].as<bool>();
}

size_t JSONConverter::estimateSignalJsonSize(const SignalData& signal) {
    size_t size = BASE_SIGNAL_SIZE;
    size += signal.name.length();
    size += signal.description.length();
    size += signal.rawData.size() * 6; // 每个uint16_t约6字节
    return size;
}

size_t JSONConverter::estimateTaskJsonSize(const TaskData& task) {
    size_t size = BASE_TASK_SIZE;
    size += task.name.length();
    size += task.description.length();
    size += task.signals.size() * 12; // 每个ID约12字节
    return size;
}

size_t JSONConverter::estimateTimerJsonSize(const TimerData& timer) {
    size_t size = BASE_TIMER_SIZE;
    size += timer.name.length();
    return size;
}

bool JSONConverter::isValidJsonSize(size_t size) {
    return size > 0 && size <= MAX_JSON_SIZE;
}

bool JSONConverter::isValidString(const String& str, size_t maxLength) {
    if (str.length() > maxLength) {
        return false;
    }

    // 检查非法字符
    for (size_t i = 0; i < str.length(); i++) {
        char c = str[i];
        if (c < 32 && c != '\t' && c != '\n' && c != '\r') {
            return false; // 控制字符（除了制表符和换行符）
        }
    }

    return true;
}

String JSONConverter::sanitizeString(const String& str, size_t maxLength) {
    if (str.length() == 0) {
        return str;
    }

    String result = str;

    // 截断长度
    if (result.length() > maxLength) {
        result = result.substring(0, maxLength);
    }

    // 移除非法字符
    String cleaned;
    cleaned.reserve(result.length());

    for (size_t i = 0; i < result.length(); i++) {
        char c = result[i];
        if (c >= 32 || c == '\t' || c == '\n' || c == '\r') {
            cleaned += c;
        }
    }

    return cleaned;
}

bool JSONConverter::isValidArraySize(size_t size) {
    return size <= MAX_ARRAY_SIZE;
}
