#pragma once

#include "DataStructures.h"
#include "IDGenerator.h"
#include <ArduinoJson.h>
#include <vector>

/**
 * ESP32-S3 红外控制系统 - JSON转换器
 * 
 * 功能：
 * 1. 数据结构与JSON互转
 * 2. 前后端数据格式统一
 * 3. 类型安全的序列化/反序列化
 * 4. 内存优化的JSON操作
 * 5. 错误处理和验证
 * 
 * 设计原则：
 * - 类型安全：强类型转换，避免运行时错误
 * - 内存优化：预分配内存，避免碎片
 * - 错误处理：完整的验证和错误报告
 * - 前端兼容：100%匹配前端数据格式
 * - 性能优化：批量操作，避免临时对象
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 */

class JSONConverter {
public:
    // ==================== 信号数据转换 ====================
    
    /**
     * 将信号数据转换为JSON对象
     * @param signal 信号数据
     * @param doc JSON文档
     * @return JSON对象
     */
    static JsonObject signalToJson(const SignalData& signal, JsonDocument& doc);
    
    /**
     * 从JSON对象创建信号数据
     * @param json JSON对象
     * @return 信号数据结果
     */
    static Result<SignalData> signalFromJson(const JsonObject& json);
    
    /**
     * 批量转换信号数据为JSON数组
     * @param signals 信号数据列表
     * @param doc JSON文档
     * @return JSON数组
     */
    static JsonArray signalsToJsonArray(const std::vector<SignalData>& signals, JsonDocument& doc);
    
    /**
     * 从JSON数组创建信号数据列表
     * @param jsonArray JSON数组
     * @return 信号数据列表结果
     */
    static Result<std::vector<SignalData>> signalsFromJsonArray(const JsonArray& jsonArray);
    
    // ==================== 任务数据转换 ====================
    
    /**
     * 将任务数据转换为JSON对象
     * @param task 任务数据
     * @param doc JSON文档
     * @return JSON对象
     */
    static JsonObject taskToJson(const TaskData& task, JsonDocument& doc);
    
    /**
     * 从JSON对象创建任务数据
     * @param json JSON对象
     * @return 任务数据结果
     */
    static Result<TaskData> taskFromJson(const JsonObject& json);
    
    /**
     * 批量转换任务数据为JSON数组
     * @param tasks 任务数据列表
     * @param doc JSON文档
     * @return JSON数组
     */
    static JsonArray tasksToJsonArray(const std::vector<TaskData>& tasks, JsonDocument& doc);
    
    // ==================== 定时器数据转换 ====================
    
    /**
     * 将定时器数据转换为JSON对象
     * @param timer 定时器数据
     * @param doc JSON文档
     * @return JSON对象
     */
    static JsonObject timerToJson(const TimerData& timer, JsonDocument& doc);
    
    /**
     * 从JSON对象创建定时器数据
     * @param json JSON对象
     * @return 定时器数据结果
     */
    static Result<TimerData> timerFromJson(const JsonObject& json);
    
    /**
     * 批量转换定时器数据为JSON数组
     * @param timers 定时器数据列表
     * @param doc JSON文档
     * @return JSON数组
     */
    static JsonArray timersToJsonArray(const std::vector<TimerData>& timers, JsonDocument& doc);
    
    // ==================== 操作结果转换 ====================
    
    /**
     * 将操作结果转换为JSON对象
     * @param result 操作结果
     * @param doc JSON文档
     * @return JSON对象
     */
    static JsonObject operationResultToJson(const OperationResultData& result, JsonDocument& doc);
    
    /**
     * 创建成功响应JSON
     * @param message 成功消息
     * @param data 附加数据（可选）
     * @param doc JSON文档
     * @return JSON对象
     */
    static JsonObject createSuccessResponse(const String& message, JsonDocument& doc, const JsonVariant& data = JsonVariant());
    
    /**
     * 创建错误响应JSON
     * @param errorCode 错误码
     * @param message 错误消息
     * @param doc JSON文档
     * @return JSON对象
     */
    static JsonObject createErrorResponse(ErrorCode errorCode, const String& message, JsonDocument& doc);
    
    // ==================== 枚举转换 ====================
    
    /**
     * 红外协议枚举转字符串
     */
    static String protocolToString(IRProtocol protocol);
    static IRProtocol protocolFromString(const String& str);
    
    /**
     * 设备类型枚举转字符串
     */
    static String deviceTypeToString(DeviceType type);
    static DeviceType deviceTypeFromString(const String& str);
    
    /**
     * 任务状态枚举转字符串
     */
    static String taskStatusToString(TaskStatus status);
    static TaskStatus taskStatusFromString(const String& str);
    
    /**
     * 任务类型枚举转字符串
     */
    static String taskTypeToString(TaskType type);
    static TaskType taskTypeFromString(const String& str);
    
    /**
     * 优先级枚举转字符串
     */
    static String priorityToString(Priority priority);
    static Priority priorityFromString(const String& str);
    
    // ==================== 验证和工具函数 ====================
    
    /**
     * 验证JSON对象是否包含必需字段
     * @param json JSON对象
     * @param requiredFields 必需字段列表
     * @return 验证结果
     */
    static bool validateRequiredFields(const JsonObject& json, const std::vector<String>& requiredFields);
    
    /**
     * 安全获取JSON字符串值
     * @param json JSON对象
     * @param key 键名
     * @param defaultValue 默认值
     * @param maxLength 最大长度
     * @return 字符串值
     */
    static String getStringValue(const JsonObject& json, const char* key, const String& defaultValue = "", size_t maxLength = 255);
    
    /**
     * 安全获取JSON整数值
     * @param json JSON对象
     * @param key 键名
     * @param defaultValue 默认值
     * @param minValue 最小值
     * @param maxValue 最大值
     * @return 整数值
     */
    static uint32_t getUIntValue(const JsonObject& json, const char* key, uint32_t defaultValue = 0, uint32_t minValue = 0, uint32_t maxValue = UINT32_MAX);
    
    /**
     * 安全获取JSON布尔值
     * @param json JSON对象
     * @param key 键名
     * @param defaultValue 默认值
     * @return 布尔值
     */
    static bool getBoolValue(const JsonObject& json, const char* key, bool defaultValue = false);
    
    /**
     * 估算JSON文档所需内存大小
     * @param signal 信号数据
     * @return 估算的内存大小
     */
    static size_t estimateSignalJsonSize(const SignalData& signal);
    
    /**
     * 估算任务JSON文档所需内存大小
     * @param task 任务数据
     * @return 估算的内存大小
     */
    static size_t estimateTaskJsonSize(const TaskData& task);
    
    /**
     * 估算定时器JSON文档所需内存大小
     * @param timer 定时器数据
     * @return 估算的内存大小
     */
    static size_t estimateTimerJsonSize(const TimerData& timer);
    
    /**
     * 验证JSON文档大小是否合理
     * @param size JSON文档大小
     * @return 是否合理
     */
    static bool isValidJsonSize(size_t size);

private:
    // 常量定义
    static const size_t MAX_JSON_SIZE = 64 * 1024;     // 64KB最大JSON大小
    static const size_t MAX_STRING_LENGTH = 255;       // 最大字符串长度
    static const size_t MAX_ARRAY_SIZE = 1000;         // 最大数组大小
    static const size_t BASE_SIGNAL_SIZE = 512;        // 基础信号JSON大小
    static const size_t BASE_TASK_SIZE = 256;          // 基础任务JSON大小
    static const size_t BASE_TIMER_SIZE = 128;         // 基础定时器JSON大小
    
    /**
     * 验证字符串长度和内容
     * @param str 字符串
     * @param maxLength 最大长度
     * @return 是否有效
     */
    static bool isValidString(const String& str, size_t maxLength = MAX_STRING_LENGTH);
    
    /**
     * 清理和验证字符串
     * @param str 输入字符串
     * @param maxLength 最大长度
     * @return 清理后的字符串
     */
    static String sanitizeString(const String& str, size_t maxLength = MAX_STRING_LENGTH);
    
    /**
     * 验证数组大小
     * @param size 数组大小
     * @return 是否有效
     */
    static bool isValidArraySize(size_t size);
};

// ==================== 便利宏定义 ====================

#define SIGNAL_TO_JSON(signal, doc) JSONConverter::signalToJson(signal, doc)
#define SIGNAL_FROM_JSON(json) JSONConverter::signalFromJson(json)
#define TASK_TO_JSON(task, doc) JSONConverter::taskToJson(task, doc)
#define TASK_FROM_JSON(json) JSONConverter::taskFromJson(json)
#define TIMER_TO_JSON(timer, doc) JSONConverter::timerToJson(timer, doc)
#define TIMER_FROM_JSON(json) JSONConverter::timerFromJson(json)

#define CREATE_SUCCESS_RESPONSE(msg, doc) JSONConverter::createSuccessResponse(msg, doc)
#define CREATE_ERROR_RESPONSE(code, msg, doc) JSONConverter::createErrorResponse(code, msg, doc)
