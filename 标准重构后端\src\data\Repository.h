#pragma once

#include "../core/DataStructures.h"
#include "../core/IDGenerator.h"
#include "../core/JSONConverter.h"
#include <LittleFS.h>
#include <vector>
#include <map>
#include <functional>
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - 基础仓库模板
 * 
 * 功能：
 * 1. 通用数据访问接口
 * 2. 文件系统持久化
 * 3. 内存缓存管理
 * 4. 事务处理支持
 * 5. 查询和过滤功能
 * 
 * 设计原则：
 * - Repository模式：统一数据访问接口
 * - 缓存优先：内存缓存提高性能
 * - 持久化：LittleFS文件系统存储
 * - 线程安全：互斥锁保护共享资源
 * - 事务支持：批量操作的原子性
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 */

template<typename T, typename ID = EntityID>
class Repository {
public:
    // 查询条件函数类型
    using FilterFunction = std::function<bool(const T&)>;
    using SortFunction = std::function<bool(const T&, const T&)>;
    
    // 事务操作类型
    enum class TransactionOperation {
        CREATE, UPDATE, DELETE
    };
    
    struct TransactionItem {
        TransactionOperation operation;
        ID id;
        T data;
        
        TransactionItem(TransactionOperation op, ID itemId, const T& itemData = T{})
            : operation(op), id(itemId), data(itemData) {}
    };

protected:
    // 内存缓存
    std::map<ID, T> m_cache;
    
    // 索引缓存
    std::map<String, std::vector<ID>> m_indexes;
    
    // 配置
    String m_dataDirectory;
    String m_fileExtension;
    size_t m_maxCacheSize;
    bool m_autoSave;
    
    // 状态
    bool m_initialized;
    bool m_cacheLoaded;
    mutable std::mutex m_mutex;
    
    // 统计信息
    uint32_t m_totalReads;
    uint32_t m_totalWrites;
    uint32_t m_cacheHits;
    uint32_t m_cacheMisses;
    
    // 事务支持
    std::vector<TransactionItem> m_pendingTransaction;
    bool m_inTransaction;

public:
    /**
     * 构造函数
     * @param dataDirectory 数据目录
     * @param fileExtension 文件扩展名
     * @param maxCacheSize 最大缓存大小
     * @param autoSave 是否自动保存
     */
    Repository(const String& dataDirectory, const String& fileExtension = ".json", 
               size_t maxCacheSize = 1000, bool autoSave = true)
        : m_dataDirectory(dataDirectory), m_fileExtension(fileExtension),
          m_maxCacheSize(maxCacheSize), m_autoSave(autoSave),
          m_initialized(false), m_cacheLoaded(false),
          m_totalReads(0), m_totalWrites(0), m_cacheHits(0), m_cacheMisses(0),
          m_inTransaction(false) {}
    
    /**
     * 虚析构函数
     */
    virtual ~Repository() {
        cleanup();
    }
    
    // ==================== 生命周期管理 ====================
    
    /**
     * 初始化仓库
     * @return 是否成功
     */
    virtual bool initialize() {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (m_initialized) {
            return true;
        }
        
        // 创建数据目录
        if (!createDataDirectory()) {
            Serial.printf("❌ Repository: Failed to create directory: %s\n", m_dataDirectory.c_str());
            return false;
        }
        
        // 加载缓存
        if (!loadCache()) {
            Serial.printf("⚠️ Repository: Failed to load cache from: %s\n", m_dataDirectory.c_str());
        }
        
        m_initialized = true;
        Serial.printf("✅ Repository: Initialized - %s\n", m_dataDirectory.c_str());
        
        return true;
    }
    
    /**
     * 清理资源
     */
    virtual void cleanup() {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (m_initialized) {
            // 保存缓存
            if (m_autoSave) {
                saveCache();
            }
            
            // 清理内存
            m_cache.clear();
            m_indexes.clear();
            m_pendingTransaction.clear();
            
            m_initialized = false;
            m_cacheLoaded = false;
            
            Serial.printf("✅ Repository: Cleanup completed - %s\n", m_dataDirectory.c_str());
        }
    }
    
    // ==================== 基础CRUD操作 ====================
    
    /**
     * 创建新项目
     * @param item 项目数据
     * @return 操作结果
     */
    virtual Result<T> create(const T& item) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (!m_initialized) {
            return Result<T>::Error(ErrorCode::SYSTEM_INITIALIZATION_FAILED, "Repository not initialized");
        }
        
        ID id = getItemId(item);
        if (id == static_cast<ID>(INVALID_ID)) {
            return Result<T>::Error(ErrorCode::INVALID_PARAMETER, "Invalid item ID");
        }
        
        // 检查是否已存在
        if (m_cache.find(id) != m_cache.end()) {
            return Result<T>::Error(ErrorCode::INVALID_PARAMETER, "Item already exists");
        }
        
        // 检查缓存大小
        if (m_cache.size() >= m_maxCacheSize) {
            if (!evictLeastRecentlyUsed()) {
                return Result<T>::Error(ErrorCode::INSUFFICIENT_MEMORY, "Cache full and eviction failed");
            }
        }
        
        // 添加到缓存
        m_cache[id] = item;
        
        // 更新索引
        updateIndexes(item);
        
        // 保存到文件
        if (m_autoSave && !saveItem(item)) {
            m_cache.erase(id);
            return Result<T>::Error(ErrorCode::STORAGE_WRITE_FAILED, "Failed to save item");
        }
        
        m_totalWrites++;
        return Result<T>::Success(item);
    }
    
    /**
     * 根据ID获取项目
     * @param id 项目ID
     * @return 操作结果
     */
    virtual Result<T> getById(ID id) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (!m_initialized) {
            return Result<T>::Error(ErrorCode::SYSTEM_INITIALIZATION_FAILED, "Repository not initialized");
        }
        
        m_totalReads++;
        
        // 先从缓存查找
        auto it = m_cache.find(id);
        if (it != m_cache.end()) {
            m_cacheHits++;
            return Result<T>::Success(it->second);
        }
        
        m_cacheMisses++;
        
        // 从文件加载
        auto result = loadItem(id);
        if (result.isSuccess()) {
            // 添加到缓存
            if (m_cache.size() < m_maxCacheSize) {
                m_cache[id] = result.getValue();
                updateIndexes(result.getValue());
            }
        }
        
        return result;
    }
    
    /**
     * 更新项目
     * @param item 项目数据
     * @return 操作结果
     */
    virtual Result<T> update(const T& item) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (!m_initialized) {
            return Result<T>::Error(ErrorCode::SYSTEM_INITIALIZATION_FAILED, "Repository not initialized");
        }
        
        ID id = getItemId(item);
        if (id == static_cast<ID>(INVALID_ID)) {
            return Result<T>::Error(ErrorCode::INVALID_PARAMETER, "Invalid item ID");
        }
        
        // 检查是否存在
        if (m_cache.find(id) == m_cache.end()) {
            // 尝试从文件加载
            auto loadResult = loadItem(id);
            if (!loadResult.isSuccess()) {
                return Result<T>::Error(ErrorCode::RESOURCE_NOT_FOUND, "Item not found");
            }
        }
        
        // 更新缓存
        m_cache[id] = item;
        
        // 更新索引
        updateIndexes(item);
        
        // 保存到文件
        if (m_autoSave && !saveItem(item)) {
            return Result<T>::Error(ErrorCode::STORAGE_WRITE_FAILED, "Failed to save item");
        }
        
        m_totalWrites++;
        return Result<T>::Success(item);
    }
    
    /**
     * 删除项目
     * @param id 项目ID
     * @return 是否成功
     */
    virtual bool deleteById(ID id) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (!m_initialized) {
            return false;
        }
        
        // 从缓存删除
        auto it = m_cache.find(id);
        if (it != m_cache.end()) {
            removeFromIndexes(it->second);
            m_cache.erase(it);
        }
        
        // 删除文件
        String filePath = getFilePath(id);
        if (LittleFS.exists(filePath)) {
            if (!LittleFS.remove(filePath)) {
                Serial.printf("⚠️ Repository: Failed to delete file: %s\n", filePath.c_str());
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取所有项目
     * @return 项目列表
     */
    virtual std::vector<T> getAll() {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (!m_initialized) {
            return {};
        }
        
        // 确保缓存已加载
        if (!m_cacheLoaded) {
            loadCache();
        }
        
        std::vector<T> result;
        result.reserve(m_cache.size());
        
        for (const auto& pair : m_cache) {
            result.push_back(pair.second);
        }
        
        return result;
    }
    
    // ==================== 查询和过滤 ====================
    
    /**
     * 根据条件查找项目
     * @param filter 过滤条件
     * @return 匹配的项目列表
     */
    virtual std::vector<T> findWhere(const FilterFunction& filter) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<T> result;
        
        for (const auto& pair : m_cache) {
            if (filter(pair.second)) {
                result.push_back(pair.second);
            }
        }
        
        return result;
    }
    
    /**
     * 查找第一个匹配的项目
     * @param filter 过滤条件
     * @return 操作结果
     */
    virtual Result<T> findFirst(const FilterFunction& filter) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        for (const auto& pair : m_cache) {
            if (filter(pair.second)) {
                return Result<T>::Success(pair.second);
            }
        }
        
        return Result<T>::Error(ErrorCode::RESOURCE_NOT_FOUND, "No matching item found");
    }
    
    /**
     * 计算匹配项目数量
     * @param filter 过滤条件
     * @return 匹配数量
     */
    virtual size_t count(const FilterFunction& filter = nullptr) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (!filter) {
            return m_cache.size();
        }
        
        size_t count = 0;
        for (const auto& pair : m_cache) {
            if (filter(pair.second)) {
                count++;
            }
        }
        
        return count;
    }
    
    /**
     * 检查是否存在匹配项目
     * @param filter 过滤条件
     * @return 是否存在
     */
    virtual bool exists(const FilterFunction& filter) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        for (const auto& pair : m_cache) {
            if (filter(pair.second)) {
                return true;
            }
        }
        
        return false;
    }

protected:
    // ==================== 纯虚函数 - 子类必须实现 ====================
    
    /**
     * 获取项目ID
     * @param item 项目数据
     * @return 项目ID
     */
    virtual ID getItemId(const T& item) = 0;
    
    /**
     * 将项目序列化为JSON
     * @param item 项目数据
     * @param doc JSON文档
     * @return JSON对象
     */
    virtual JsonObject itemToJson(const T& item, JsonDocument& doc) = 0;
    
    /**
     * 从JSON反序列化项目
     * @param json JSON对象
     * @return 项目数据结果
     */
    virtual Result<T> itemFromJson(const JsonObject& json) = 0;

    // ==================== 内部实现方法 ====================

    /**
     * 创建数据目录
     * @return 是否成功
     */
    virtual bool createDataDirectory() {
        if (!LittleFS.exists(m_dataDirectory)) {
            return LittleFS.mkdir(m_dataDirectory);
        }
        return true;
    }

    /**
     * 获取文件路径
     * @param id 项目ID
     * @return 文件路径
     */
    virtual String getFilePath(ID id) {
        return m_dataDirectory + "/" + String(static_cast<uint32_t>(id)) + m_fileExtension;
    }

    /**
     * 保存单个项目到文件
     * @param item 项目数据
     * @return 是否成功
     */
    virtual bool saveItem(const T& item) {
        ID id = getItemId(item);
        String filePath = getFilePath(id);

        File file = LittleFS.open(filePath, "w");
        if (!file) {
            Serial.printf("❌ Repository: Failed to open file for writing: %s\n", filePath.c_str());
            return false;
        }

        DynamicJsonDocument doc(JSONConverter::estimateSignalJsonSize(item) + 256);
        JsonObject jsonObj = itemToJson(item, doc);

        size_t written = serializeJson(doc, file);
        file.close();

        if (written == 0) {
            Serial.printf("❌ Repository: Failed to write JSON to file: %s\n", filePath.c_str());
            return false;
        }

        return true;
    }

    /**
     * 从文件加载单个项目
     * @param id 项目ID
     * @return 操作结果
     */
    virtual Result<T> loadItem(ID id) {
        String filePath = getFilePath(id);

        if (!LittleFS.exists(filePath)) {
            return Result<T>::Error(ErrorCode::RESOURCE_NOT_FOUND, "File not found");
        }

        File file = LittleFS.open(filePath, "r");
        if (!file) {
            return Result<T>::Error(ErrorCode::STORAGE_READ_FAILED, "Failed to open file");
        }

        DynamicJsonDocument doc(file.size() + 256);
        DeserializationError error = deserializeJson(doc, file);
        file.close();

        if (error) {
            Serial.printf("❌ Repository: JSON parse error in file %s: %s\n",
                         filePath.c_str(), error.c_str());
            return Result<T>::Error(ErrorCode::STORAGE_CORRUPTION, "JSON parse error");
        }

        return itemFromJson(doc.as<JsonObject>());
    }

    /**
     * 加载所有缓存
     * @return 是否成功
     */
    virtual bool loadCache() {
        if (m_cacheLoaded) {
            return true;
        }

        File dir = LittleFS.open(m_dataDirectory);
        if (!dir || !dir.isDirectory()) {
            Serial.printf("⚠️ Repository: Directory not found: %s\n", m_dataDirectory.c_str());
            return false;
        }

        size_t loadedCount = 0;
        File file = dir.openNextFile();

        while (file && loadedCount < m_maxCacheSize) {
            if (!file.isDirectory() && file.name().endsWith(m_fileExtension)) {
                // 从文件名提取ID
                String fileName = file.name();
                String idStr = fileName.substring(0, fileName.lastIndexOf('.'));
                ID id = static_cast<ID>(idStr.toInt());

                if (id != static_cast<ID>(INVALID_ID)) {
                    DynamicJsonDocument doc(file.size() + 256);
                    DeserializationError error = deserializeJson(doc, file);

                    if (!error) {
                        auto result = itemFromJson(doc.as<JsonObject>());
                        if (result.isSuccess()) {
                            m_cache[id] = result.getValue();
                            updateIndexes(result.getValue());
                            loadedCount++;
                        }
                    }
                }
            }
            file.close();
            file = dir.openNextFile();
        }

        dir.close();
        m_cacheLoaded = true;

        Serial.printf("✅ Repository: Loaded %d items from %s\n",
                     loadedCount, m_dataDirectory.c_str());

        return true;
    }

    /**
     * 保存所有缓存
     * @return 是否成功
     */
    virtual bool saveCache() {
        bool success = true;

        for (const auto& pair : m_cache) {
            if (!saveItem(pair.second)) {
                success = false;
            }
        }

        return success;
    }

    /**
     * 更新索引
     * @param item 项目数据
     */
    virtual void updateIndexes(const T& item) {
        // 基类提供空实现，子类可以重写
    }

    /**
     * 从索引中移除
     * @param item 项目数据
     */
    virtual void removeFromIndexes(const T& item) {
        // 基类提供空实现，子类可以重写
    }

    /**
     * 淘汰最少使用的项目
     * @return 是否成功
     */
    virtual bool evictLeastRecentlyUsed() {
        if (m_cache.empty()) {
            return false;
        }

        // 简单实现：删除第一个项目
        auto it = m_cache.begin();
        removeFromIndexes(it->second);
        m_cache.erase(it);

        return true;
    }

public:
    // ==================== 事务支持 ====================

    /**
     * 开始事务
     */
    virtual void beginTransaction() {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_pendingTransaction.clear();
        m_inTransaction = true;
    }

    /**
     * 提交事务
     * @return 是否成功
     */
    virtual bool commitTransaction() {
        std::lock_guard<std::mutex> lock(m_mutex);

        if (!m_inTransaction) {
            return false;
        }

        // 执行所有操作
        for (const auto& item : m_pendingTransaction) {
            switch (item.operation) {
                case TransactionOperation::CREATE:
                    if (!create(item.data).isSuccess()) {
                        rollbackTransaction();
                        return false;
                    }
                    break;
                case TransactionOperation::UPDATE:
                    if (!update(item.data).isSuccess()) {
                        rollbackTransaction();
                        return false;
                    }
                    break;
                case TransactionOperation::DELETE:
                    if (!deleteById(item.id)) {
                        rollbackTransaction();
                        return false;
                    }
                    break;
            }
        }

        m_pendingTransaction.clear();
        m_inTransaction = false;
        return true;
    }

    /**
     * 回滚事务
     */
    virtual void rollbackTransaction() {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_pendingTransaction.clear();
        m_inTransaction = false;
    }

    // ==================== 统计和监控 ====================

    /**
     * 获取统计信息
     * @param doc JSON文档
     * @return JSON对象
     */
    virtual JsonObject getStatistics(JsonDocument& doc) {
        std::lock_guard<std::mutex> lock(m_mutex);

        JsonObject stats = doc.createNestedObject("repository");
        stats["initialized"] = m_initialized;
        stats["cache_loaded"] = m_cacheLoaded;
        stats["cache_size"] = m_cache.size();
        stats["max_cache_size"] = m_maxCacheSize;
        stats["total_reads"] = m_totalReads;
        stats["total_writes"] = m_totalWrites;
        stats["cache_hits"] = m_cacheHits;
        stats["cache_misses"] = m_cacheMisses;

        if (m_totalReads > 0) {
            stats["cache_hit_rate"] = (float)m_cacheHits / m_totalReads;
        }

        stats["data_directory"] = m_dataDirectory;
        stats["auto_save"] = m_autoSave;
        stats["in_transaction"] = m_inTransaction;

        return stats;
    }

    /**
     * 清理缓存
     */
    virtual void clearCache() {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_cache.clear();
        m_indexes.clear();
        m_cacheLoaded = false;
    }

    /**
     * 强制保存所有数据
     * @return 是否成功
     */
    virtual bool flush() {
        std::lock_guard<std::mutex> lock(m_mutex);
        return saveCache();
    }
};
