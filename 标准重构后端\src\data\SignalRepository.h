#pragma once

#include "Repository.h"
#include "../core/DataStructures.h"
#include "../core/JSONConverter.h"

/**
 * ESP32-S3 红外控制系统 - 信号数据仓库
 * 
 * 功能：
 * 1. 信号数据的CRUD操作
 * 2. 按协议、设备类型索引
 * 3. 信号搜索和过滤
 * 4. 使用统计管理
 * 5. 批量操作支持
 * 
 * 设计原则：
 * - 继承Repository基类
 * - 专门针对SignalData优化
 * - 支持多种查询方式
 * - 自动维护索引
 * - 统计信息跟踪
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 */

class SignalRepository : public Repository<SignalData, SignalID> {
private:
    // 专用索引
    std::map<IRProtocol, std::vector<SignalID>> m_protocolIndex;
    std::map<DeviceType, std::vector<SignalID>> m_deviceTypeIndex;
    std::map<String, std::vector<SignalID>> m_nameIndex;
    
    // 使用统计
    std::map<SignalID, uint32_t> m_usageStats;
    
    // 配置
    bool m_enableUsageTracking;
    size_t m_maxSignals;

public:
    /**
     * 构造函数
     * @param enableUsageTracking 是否启用使用统计
     * @param maxSignals 最大信号数量
     */
    SignalRepository(bool enableUsageTracking = true, size_t maxSignals = 10000)
        : Repository("/signals", ".json", maxSignals, true),
          m_enableUsageTracking(enableUsageTracking),
          m_maxSignals(maxSignals) {}
    
    /**
     * 析构函数
     */
    virtual ~SignalRepository() = default;
    
    // ==================== 专用查询方法 ====================
    
    /**
     * 根据协议查找信号
     * @param protocol 红外协议
     * @return 信号列表
     */
    std::vector<SignalData> findByProtocol(IRProtocol protocol) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<SignalData> result;
        auto it = m_protocolIndex.find(protocol);
        
        if (it != m_protocolIndex.end()) {
            result.reserve(it->second.size());
            for (SignalID id : it->second) {
                auto cacheIt = m_cache.find(id);
                if (cacheIt != m_cache.end()) {
                    result.push_back(cacheIt->second);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 根据设备类型查找信号
     * @param deviceType 设备类型
     * @return 信号列表
     */
    std::vector<SignalData> findByDeviceType(DeviceType deviceType) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<SignalData> result;
        auto it = m_deviceTypeIndex.find(deviceType);
        
        if (it != m_deviceTypeIndex.end()) {
            result.reserve(it->second.size());
            for (SignalID id : it->second) {
                auto cacheIt = m_cache.find(id);
                if (cacheIt != m_cache.end()) {
                    result.push_back(cacheIt->second);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 根据名称搜索信号（模糊匹配）
     * @param namePattern 名称模式
     * @param caseSensitive 是否区分大小写
     * @return 信号列表
     */
    std::vector<SignalData> searchByName(const String& namePattern, bool caseSensitive = false) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<SignalData> result;
        String pattern = caseSensitive ? namePattern : namePattern;
        if (!caseSensitive) {
            pattern.toLowerCase();
        }
        
        for (const auto& pair : m_cache) {
            String signalName = caseSensitive ? pair.second.name : pair.second.name;
            if (!caseSensitive) {
                signalName.toLowerCase();
            }
            
            if (signalName.indexOf(pattern) >= 0) {
                result.push_back(pair.second);
            }
        }
        
        return result;
    }
    
    /**
     * 根据信号码查找信号
     * @param code 信号码
     * @return 操作结果
     */
    Result<SignalData> findByCode(uint64_t code) {
        return findFirst([code](const SignalData& signal) {
            return signal.code == code;
        });
    }
    
    /**
     * 获取最常用的信号
     * @param limit 返回数量限制
     * @return 信号列表
     */
    std::vector<SignalData> getMostUsed(size_t limit = 10) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<std::pair<SignalID, uint32_t>> usageList;
        for (const auto& pair : m_usageStats) {
            usageList.push_back(pair);
        }
        
        // 按使用次数排序
        std::sort(usageList.begin(), usageList.end(),
                 [](const auto& a, const auto& b) {
                     return a.second > b.second;
                 });
        
        std::vector<SignalData> result;
        result.reserve(std::min(limit, usageList.size()));
        
        for (size_t i = 0; i < std::min(limit, usageList.size()); i++) {
            auto cacheIt = m_cache.find(usageList[i].first);
            if (cacheIt != m_cache.end()) {
                result.push_back(cacheIt->second);
            }
        }
        
        return result;
    }
    
    /**
     * 获取最近使用的信号
     * @param limit 返回数量限制
     * @return 信号列表
     */
    std::vector<SignalData> getRecentlyUsed(size_t limit = 10) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<SignalData> allSignals;
        for (const auto& pair : m_cache) {
            allSignals.push_back(pair.second);
        }
        
        // 按最后使用时间排序
        std::sort(allSignals.begin(), allSignals.end(),
                 [](const SignalData& a, const SignalData& b) {
                     return a.lastUsed > b.lastUsed;
                 });
        
        std::vector<SignalData> result;
        result.reserve(std::min(limit, allSignals.size()));
        
        for (size_t i = 0; i < std::min(limit, allSignals.size()); i++) {
            result.push_back(allSignals[i]);
        }
        
        return result;
    }
    
    // ==================== 使用统计管理 ====================
    
    /**
     * 记录信号使用
     * @param signalId 信号ID
     */
    void recordUsage(SignalID signalId) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (!m_enableUsageTracking) {
            return;
        }
        
        // 更新使用统计
        m_usageStats[signalId]++;
        
        // 更新信号数据中的使用信息
        auto it = m_cache.find(signalId);
        if (it != m_cache.end()) {
            it->second.usageCount++;
            it->second.lastUsed = millis();
            
            // 自动保存更新
            if (m_autoSave) {
                saveItem(it->second);
            }
        }
    }
    
    /**
     * 获取信号使用次数
     * @param signalId 信号ID
     * @return 使用次数
     */
    uint32_t getUsageCount(SignalID signalId) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto it = m_usageStats.find(signalId);
        return (it != m_usageStats.end()) ? it->second : 0;
    }
    
    /**
     * 重置使用统计
     * @param signalId 信号ID（可选，不指定则重置所有）
     */
    void resetUsageStats(SignalID signalId = INVALID_ID) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (signalId == INVALID_ID) {
            // 重置所有统计
            m_usageStats.clear();
            for (auto& pair : m_cache) {
                pair.second.usageCount = 0;
                pair.second.lastUsed = 0;
            }
        } else {
            // 重置指定信号统计
            m_usageStats.erase(signalId);
            auto it = m_cache.find(signalId);
            if (it != m_cache.end()) {
                it->second.usageCount = 0;
                it->second.lastUsed = 0;
            }
        }
    }
    
    // ==================== 批量操作 ====================
    
    /**
     * 批量创建信号
     * @param signals 信号列表
     * @return 成功创建的数量
     */
    size_t createBatch(const std::vector<SignalData>& signals) {
        beginTransaction();
        
        size_t successCount = 0;
        for (const auto& signal : signals) {
            if (create(signal).isSuccess()) {
                successCount++;
            } else {
                rollbackTransaction();
                return 0;
            }
        }
        
        if (commitTransaction()) {
            return successCount;
        } else {
            return 0;
        }
    }
    
    /**
     * 批量删除信号
     * @param signalIds 信号ID列表
     * @return 成功删除的数量
     */
    size_t deleteBatch(const std::vector<SignalID>& signalIds) {
        beginTransaction();
        
        size_t successCount = 0;
        for (SignalID id : signalIds) {
            if (deleteById(id)) {
                successCount++;
            } else {
                rollbackTransaction();
                return 0;
            }
        }
        
        if (commitTransaction()) {
            return successCount;
        } else {
            return 0;
        }
    }
    
    // ==================== 统计信息 ====================
    
    /**
     * 获取协议分布统计
     * @return 协议统计映射
     */
    std::map<IRProtocol, size_t> getProtocolStats() {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::map<IRProtocol, size_t> stats;
        for (const auto& pair : m_protocolIndex) {
            stats[pair.first] = pair.second.size();
        }
        
        return stats;
    }
    
    /**
     * 获取设备类型分布统计
     * @return 设备类型统计映射
     */
    std::map<DeviceType, size_t> getDeviceTypeStats() {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::map<DeviceType, size_t> stats;
        for (const auto& pair : m_deviceTypeIndex) {
            stats[pair.first] = pair.second.size();
        }
        
        return stats;
    }
    
    /**
     * 获取详细统计信息
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getDetailedStatistics(JsonDocument& doc) override {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        JsonObject stats = Repository::getStatistics(doc);
        
        // 协议统计
        JsonObject protocolStats = stats.createNestedObject("protocol_stats");
        for (const auto& pair : m_protocolIndex) {
            protocolStats[JSONConverter::protocolToString(pair.first)] = pair.second.size();
        }
        
        // 设备类型统计
        JsonObject deviceStats = stats.createNestedObject("device_type_stats");
        for (const auto& pair : m_deviceTypeIndex) {
            deviceStats[JSONConverter::deviceTypeToString(pair.first)] = pair.second.size();
        }
        
        // 使用统计
        stats["usage_tracking_enabled"] = m_enableUsageTracking;
        stats["total_usage_records"] = m_usageStats.size();
        
        if (!m_usageStats.empty()) {
            uint32_t totalUsage = 0;
            uint32_t maxUsage = 0;
            for (const auto& pair : m_usageStats) {
                totalUsage += pair.second;
                maxUsage = std::max(maxUsage, pair.second);
            }
            stats["total_usage_count"] = totalUsage;
            stats["max_usage_count"] = maxUsage;
            stats["average_usage"] = (float)totalUsage / m_usageStats.size();
        }
        
        return stats;
    }

protected:
    // ==================== Repository基类实现 ====================
    
    SignalID getItemId(const SignalData& item) override {
        return item.id;
    }
    
    JsonObject itemToJson(const SignalData& item, JsonDocument& doc) override {
        return JSONConverter::signalToJson(item, doc);
    }
    
    Result<SignalData> itemFromJson(const JsonObject& json) override {
        return JSONConverter::signalFromJson(json);
    }

    /**
     * 更新索引
     * @param item 信号数据
     */
    void updateIndexes(const SignalData& item) override {
        SignalID id = item.id;

        // 更新协议索引
        auto& protocolList = m_protocolIndex[item.protocol];
        if (std::find(protocolList.begin(), protocolList.end(), id) == protocolList.end()) {
            protocolList.push_back(id);
        }

        // 更新设备类型索引
        auto& deviceList = m_deviceTypeIndex[item.deviceType];
        if (std::find(deviceList.begin(), deviceList.end(), id) == deviceList.end()) {
            deviceList.push_back(id);
        }

        // 更新名称索引（按首字母分组）
        if (!item.name.isEmpty()) {
            String firstChar = item.name.substring(0, 1);
            firstChar.toUpperCase();
            auto& nameList = m_nameIndex[firstChar];
            if (std::find(nameList.begin(), nameList.end(), id) == nameList.end()) {
                nameList.push_back(id);
            }
        }

        // 更新使用统计
        if (m_enableUsageTracking) {
            if (m_usageStats.find(id) == m_usageStats.end()) {
                m_usageStats[id] = item.usageCount;
            }
        }
    }

    /**
     * 从索引中移除
     * @param item 信号数据
     */
    void removeFromIndexes(const SignalData& item) override {
        SignalID id = item.id;

        // 从协议索引移除
        auto protocolIt = m_protocolIndex.find(item.protocol);
        if (protocolIt != m_protocolIndex.end()) {
            auto& list = protocolIt->second;
            list.erase(std::remove(list.begin(), list.end(), id), list.end());
            if (list.empty()) {
                m_protocolIndex.erase(protocolIt);
            }
        }

        // 从设备类型索引移除
        auto deviceIt = m_deviceTypeIndex.find(item.deviceType);
        if (deviceIt != m_deviceTypeIndex.end()) {
            auto& list = deviceIt->second;
            list.erase(std::remove(list.begin(), list.end(), id), list.end());
            if (list.empty()) {
                m_deviceTypeIndex.erase(deviceIt);
            }
        }

        // 从名称索引移除
        if (!item.name.isEmpty()) {
            String firstChar = item.name.substring(0, 1);
            firstChar.toUpperCase();
            auto nameIt = m_nameIndex.find(firstChar);
            if (nameIt != m_nameIndex.end()) {
                auto& list = nameIt->second;
                list.erase(std::remove(list.begin(), list.end(), id), list.end());
                if (list.empty()) {
                    m_nameIndex.erase(nameIt);
                }
            }
        }

        // 从使用统计移除
        m_usageStats.erase(id);
    }

    /**
     * 淘汰最少使用的信号
     * @return 是否成功
     */
    bool evictLeastRecentlyUsed() override {
        if (m_cache.empty()) {
            return false;
        }

        // 找到最少使用的信号
        SignalID leastUsedId = INVALID_ID;
        uint32_t minUsage = UINT32_MAX;
        Timestamp oldestTime = UINT32_MAX;

        for (const auto& pair : m_cache) {
            uint32_t usage = getUsageCount(pair.first);
            Timestamp lastUsed = pair.second.lastUsed;

            // 优先淘汰使用次数少的，其次淘汰最久未使用的
            if (usage < minUsage || (usage == minUsage && lastUsed < oldestTime)) {
                minUsage = usage;
                oldestTime = lastUsed;
                leastUsedId = pair.first;
            }
        }

        if (leastUsedId != INVALID_ID) {
            auto it = m_cache.find(leastUsedId);
            if (it != m_cache.end()) {
                removeFromIndexes(it->second);
                m_cache.erase(it);
                return true;
            }
        }

        return false;
    }

public:
    // ==================== 高级查询功能 ====================

    /**
     * 复合查询：按协议和设备类型
     * @param protocol 红外协议
     * @param deviceType 设备类型
     * @return 信号列表
     */
    std::vector<SignalData> findByProtocolAndDevice(IRProtocol protocol, DeviceType deviceType) {
        return findWhere([protocol, deviceType](const SignalData& signal) {
            return signal.protocol == protocol && signal.deviceType == deviceType;
        });
    }

    /**
     * 按频率范围查找信号
     * @param minFreq 最小频率
     * @param maxFreq 最大频率
     * @return 信号列表
     */
    std::vector<SignalData> findByFrequencyRange(uint16_t minFreq, uint16_t maxFreq) {
        return findWhere([minFreq, maxFreq](const SignalData& signal) {
            return signal.frequency >= minFreq && signal.frequency <= maxFreq;
        });
    }

    /**
     * 按创建时间范围查找信号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 信号列表
     */
    std::vector<SignalData> findByTimeRange(Timestamp startTime, Timestamp endTime) {
        return findWhere([startTime, endTime](const SignalData& signal) {
            return signal.createdTime >= startTime && signal.createdTime <= endTime;
        });
    }

    /**
     * 获取孤儿信号（从未使用过的信号）
     * @return 信号列表
     */
    std::vector<SignalData> getOrphanSignals() {
        return findWhere([](const SignalData& signal) {
            return signal.usageCount == 0;
        });
    }

    /**
     * 验证信号数据完整性
     * @return 验证结果
     */
    struct ValidationResult {
        bool isValid;
        std::vector<String> errors;
        std::vector<String> warnings;
        size_t totalSignals;
        size_t validSignals;
        size_t invalidSignals;
    };

    ValidationResult validateAllSignals() {
        std::lock_guard<std::mutex> lock(m_mutex);

        ValidationResult result;
        result.isValid = true;
        result.totalSignals = m_cache.size();
        result.validSignals = 0;
        result.invalidSignals = 0;

        for (const auto& pair : m_cache) {
            const SignalData& signal = pair.second;

            // 基础验证
            if (!signal.isValid()) {
                result.invalidSignals++;
                result.errors.push_back("Signal " + String(signal.id) + " is invalid");
                result.isValid = false;
                continue;
            }

            // 名称验证
            if (signal.name.isEmpty()) {
                result.warnings.push_back("Signal " + String(signal.id) + " has empty name");
            }

            // 协议验证
            if (signal.protocol == IRProtocol::UNKNOWN) {
                result.warnings.push_back("Signal " + String(signal.id) + " has unknown protocol");
            }

            // 频率验证
            if (signal.frequency < 30000 || signal.frequency > 60000) {
                result.warnings.push_back("Signal " + String(signal.id) + " has unusual frequency: " + String(signal.frequency));
            }

            // 数据验证
            if (signal.code == 0 && signal.rawData.empty()) {
                result.errors.push_back("Signal " + String(signal.id) + " has no signal data");
                result.invalidSignals++;
                result.isValid = false;
                continue;
            }

            result.validSignals++;
        }

        return result;
    }

    /**
     * 清理无效信号
     * @return 清理的信号数量
     */
    size_t cleanupInvalidSignals() {
        auto validation = validateAllSignals();

        if (validation.invalidSignals == 0) {
            return 0;
        }

        std::vector<SignalID> toDelete;

        for (const auto& pair : m_cache) {
            if (!pair.second.isValid()) {
                toDelete.push_back(pair.first);
            }
        }

        size_t deletedCount = 0;
        for (SignalID id : toDelete) {
            if (deleteById(id)) {
                deletedCount++;
            }
        }

        return deletedCount;
    }
};
