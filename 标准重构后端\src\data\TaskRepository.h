#pragma once

#include "Repository.h"
#include "../core/DataStructures.h"
#include "../core/JSONConverter.h"

/**
 * ESP32-S3 红外控制系统 - 任务数据仓库
 * 
 * 功能：
 * 1. 任务数据的CRUD操作
 * 2. 按状态、类型、优先级索引
 * 3. 任务调度和执行管理
 * 4. 执行统计和监控
 * 5. 任务队列管理
 * 
 * 设计原则：
 * - 继承Repository基类
 * - 专门针对TaskData优化
 * - 支持任务调度查询
 * - 自动维护执行统计
 * - 任务状态管理
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 */

class TaskRepository : public Repository<TaskData, TaskID> {
private:
    // 专用索引
    std::map<TaskStatus, std::vector<TaskID>> m_statusIndex;
    std::map<TaskType, std::vector<TaskID>> m_typeIndex;
    std::map<Priority, std::vector<TaskID>> m_priorityIndex;
    
    // 调度索引
    std::multimap<Timestamp, TaskID> m_scheduleIndex;  // 按执行时间排序
    
    // 执行统计
    std::map<TaskID, uint32_t> m_executionStats;
    
    // 配置
    bool m_enableScheduling;
    size_t m_maxTasks;
    uint32_t m_defaultTimeout;

public:
    /**
     * 构造函数
     * @param enableScheduling 是否启用调度功能
     * @param maxTasks 最大任务数量
     * @param defaultTimeout 默认超时时间（毫秒）
     */
    TaskRepository(bool enableScheduling = true, size_t maxTasks = 1000, uint32_t defaultTimeout = 30000)
        : Repository("/tasks", ".json", maxTasks, true),
          m_enableScheduling(enableScheduling),
          m_maxTasks(maxTasks),
          m_defaultTimeout(defaultTimeout) {}
    
    /**
     * 析构函数
     */
    virtual ~TaskRepository() = default;
    
    // ==================== 专用查询方法 ====================
    
    /**
     * 根据状态查找任务
     * @param status 任务状态
     * @return 任务列表
     */
    std::vector<TaskData> findByStatus(TaskStatus status) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<TaskData> result;
        auto it = m_statusIndex.find(status);
        
        if (it != m_statusIndex.end()) {
            result.reserve(it->second.size());
            for (TaskID id : it->second) {
                auto cacheIt = m_cache.find(id);
                if (cacheIt != m_cache.end()) {
                    result.push_back(cacheIt->second);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 根据类型查找任务
     * @param type 任务类型
     * @return 任务列表
     */
    std::vector<TaskData> findByType(TaskType type) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<TaskData> result;
        auto it = m_typeIndex.find(type);
        
        if (it != m_typeIndex.end()) {
            result.reserve(it->second.size());
            for (TaskID id : it->second) {
                auto cacheIt = m_cache.find(id);
                if (cacheIt != m_cache.end()) {
                    result.push_back(cacheIt->second);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 根据优先级查找任务
     * @param priority 优先级
     * @return 任务列表
     */
    std::vector<TaskData> findByPriority(Priority priority) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<TaskData> result;
        auto it = m_priorityIndex.find(priority);
        
        if (it != m_priorityIndex.end()) {
            result.reserve(it->second.size());
            for (TaskID id : it->second) {
                auto cacheIt = m_cache.find(id);
                if (cacheIt != m_cache.end()) {
                    result.push_back(cacheIt->second);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 获取待执行的任务
     * @param currentTime 当前时间
     * @param limit 返回数量限制
     * @return 任务列表
     */
    std::vector<TaskData> getPendingTasks(Timestamp currentTime = 0, size_t limit = 100) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (currentTime == 0) {
            currentTime = millis();
        }
        
        std::vector<TaskData> result;
        result.reserve(std::min(limit, m_cache.size()));
        
        // 从调度索引中查找到期的任务
        auto endIt = m_scheduleIndex.upper_bound(currentTime);
        
        for (auto it = m_scheduleIndex.begin(); it != endIt && result.size() < limit; ++it) {
            auto cacheIt = m_cache.find(it->second);
            if (cacheIt != m_cache.end()) {
                const TaskData& task = cacheIt->second;
                if (task.enabled && task.status == TaskStatus::PENDING) {
                    result.push_back(task);
                }
            }
        }
        
        // 按优先级排序
        std::sort(result.begin(), result.end(), [](const TaskData& a, const TaskData& b) {
            return static_cast<int>(a.priority) > static_cast<int>(b.priority);
        });
        
        return result;
    }
    
    /**
     * 获取正在运行的任务
     * @return 任务列表
     */
    std::vector<TaskData> getRunningTasks() {
        return findByStatus(TaskStatus::RUNNING);
    }
    
    /**
     * 获取已完成的任务
     * @param limit 返回数量限制
     * @return 任务列表
     */
    std::vector<TaskData> getCompletedTasks(size_t limit = 50) {
        auto completed = findByStatus(TaskStatus::COMPLETED);
        
        // 按完成时间排序（最新的在前）
        std::sort(completed.begin(), completed.end(), [](const TaskData& a, const TaskData& b) {
            return a.lastExecuted > b.lastExecuted;
        });
        
        if (completed.size() > limit) {
            completed.resize(limit);
        }
        
        return completed;
    }
    
    /**
     * 获取失败的任务
     * @return 任务列表
     */
    std::vector<TaskData> getFailedTasks() {
        return findByStatus(TaskStatus::FAILED);
    }
    
    // ==================== 任务状态管理 ====================
    
    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @param newStatus 新状态
     * @param updateTime 是否更新时间戳
     * @return 是否成功
     */
    bool updateTaskStatus(TaskID taskId, TaskStatus newStatus, bool updateTime = true) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto it = m_cache.find(taskId);
        if (it == m_cache.end()) {
            return false;
        }
        
        TaskData& task = it->second;
        TaskStatus oldStatus = task.status;
        
        // 更新状态
        task.status = newStatus;
        
        if (updateTime) {
            task.modifiedTime = millis();
            
            if (newStatus == TaskStatus::RUNNING) {
                task.lastExecuted = task.modifiedTime;
            } else if (newStatus == TaskStatus::COMPLETED) {
                task.successCount++;
                task.executionCount++;
            } else if (newStatus == TaskStatus::FAILED) {
                task.failureCount++;
                task.executionCount++;
            }
        }
        
        // 更新索引
        updateStatusIndex(taskId, oldStatus, newStatus);
        
        // 自动保存
        if (m_autoSave) {
            saveItem(task);
        }
        
        return true;
    }
    
    /**
     * 标记任务开始执行
     * @param taskId 任务ID
     * @return 是否成功
     */
    bool startTask(TaskID taskId) {
        return updateTaskStatus(taskId, TaskStatus::RUNNING, true);
    }
    
    /**
     * 标记任务完成
     * @param taskId 任务ID
     * @param success 是否成功
     * @return 是否成功
     */
    bool completeTask(TaskID taskId, bool success = true) {
        TaskStatus newStatus = success ? TaskStatus::COMPLETED : TaskStatus::FAILED;
        
        if (!updateTaskStatus(taskId, newStatus, true)) {
            return false;
        }
        
        // 如果是重复任务，重新调度
        auto it = m_cache.find(taskId);
        if (it != m_cache.end() && it->second.type == TaskType::REPEATED) {
            scheduleNextExecution(it->second);
        }
        
        return true;
    }
    
    /**
     * 暂停任务
     * @param taskId 任务ID
     * @return 是否成功
     */
    bool pauseTask(TaskID taskId) {
        return updateTaskStatus(taskId, TaskStatus::PAUSED, true);
    }
    
    /**
     * 恢复任务
     * @param taskId 任务ID
     * @return 是否成功
     */
    bool resumeTask(TaskID taskId) {
        return updateTaskStatus(taskId, TaskStatus::PENDING, true);
    }
    
    /**
     * 取消任务
     * @param taskId 任务ID
     * @return 是否成功
     */
    bool cancelTask(TaskID taskId) {
        return updateTaskStatus(taskId, TaskStatus::CANCELLED, true);
    }
    
    // ==================== 调度管理 ====================
    
    /**
     * 调度任务的下次执行
     * @param task 任务数据
     * @return 是否成功
     */
    bool scheduleNextExecution(const TaskData& task) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (!m_enableScheduling || !task.enabled) {
            return false;
        }
        
        Timestamp nextTime = calculateNextExecutionTime(task);
        if (nextTime == 0) {
            return false;
        }
        
        // 更新任务的下次执行时间
        auto it = m_cache.find(task.id);
        if (it != m_cache.end()) {
            it->second.nextExecution = nextTime;
            it->second.status = TaskStatus::PENDING;
            
            // 更新调度索引
            updateScheduleIndex(task.id, nextTime);
            
            // 自动保存
            if (m_autoSave) {
                saveItem(it->second);
            }
        }
        
        return true;
    }
    
    /**
     * 计算任务的下次执行时间
     * @param task 任务数据
     * @return 下次执行时间
     */
    Timestamp calculateNextExecutionTime(const TaskData& task) {
        Timestamp currentTime = millis();
        
        switch (task.type) {
            case TaskType::SINGLE_SIGNAL:
                // 单次任务，立即执行
                return currentTime;
                
            case TaskType::BATCH_SIGNALS:
                // 批量任务，立即执行
                return currentTime;
                
            case TaskType::SCHEDULED:
                // 定时任务，使用nextExecution时间
                return task.nextExecution > currentTime ? task.nextExecution : currentTime;
                
            case TaskType::REPEATED:
                // 重复任务，根据间隔计算
                if (task.interval > 0) {
                    return currentTime + task.interval;
                }
                break;
                
            case TaskType::CONDITIONAL:
                // 条件任务，需要外部触发
                return 0;
        }
        
        return 0;
    }
    
    /**
     * 清理过期的已完成任务
     * @param maxAge 最大保留时间（毫秒）
     * @return 清理的任务数量
     */
    size_t cleanupCompletedTasks(uint32_t maxAge = 7 * 24 * 60 * 60 * 1000) { // 默认7天
        std::lock_guard<std::mutex> lock(m_mutex);
        
        Timestamp cutoffTime = millis() - maxAge;
        std::vector<TaskID> toDelete;
        
        for (const auto& pair : m_cache) {
            const TaskData& task = pair.second;
            if ((task.status == TaskStatus::COMPLETED || task.status == TaskStatus::FAILED) &&
                task.lastExecuted < cutoffTime) {
                toDelete.push_back(pair.first);
            }
        }
        
        size_t deletedCount = 0;
        for (TaskID id : toDelete) {
            if (deleteById(id)) {
                deletedCount++;
            }
        }
        
        return deletedCount;
    }

    // ==================== 统计和监控 ====================

    /**
     * 获取任务执行统计
     * @return 统计信息映射
     */
    std::map<TaskStatus, size_t> getStatusStats() {
        std::lock_guard<std::mutex> lock(m_mutex);

        std::map<TaskStatus, size_t> stats;
        for (const auto& pair : m_statusIndex) {
            stats[pair.first] = pair.second.size();
        }

        return stats;
    }

    /**
     * 获取任务类型统计
     * @return 统计信息映射
     */
    std::map<TaskType, size_t> getTypeStats() {
        std::lock_guard<std::mutex> lock(m_mutex);

        std::map<TaskType, size_t> stats;
        for (const auto& pair : m_typeIndex) {
            stats[pair.first] = pair.second.size();
        }

        return stats;
    }

    /**
     * 获取优先级统计
     * @return 统计信息映射
     */
    std::map<Priority, size_t> getPriorityStats() {
        std::lock_guard<std::mutex> lock(m_mutex);

        std::map<Priority, size_t> stats;
        for (const auto& pair : m_priorityIndex) {
            stats[pair.first] = pair.second.size();
        }

        return stats;
    }

    /**
     * 获取详细统计信息
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getDetailedStatistics(JsonDocument& doc) override {
        std::lock_guard<std::mutex> lock(m_mutex);

        JsonObject stats = Repository::getStatistics(doc);

        // 状态统计
        JsonObject statusStats = stats.createNestedObject("status_stats");
        for (const auto& pair : m_statusIndex) {
            statusStats[JSONConverter::taskStatusToString(pair.first)] = pair.second.size();
        }

        // 类型统计
        JsonObject typeStats = stats.createNestedObject("type_stats");
        for (const auto& pair : m_typeIndex) {
            typeStats[JSONConverter::taskTypeToString(pair.first)] = pair.second.size();
        }

        // 优先级统计
        JsonObject priorityStats = stats.createNestedObject("priority_stats");
        for (const auto& pair : m_priorityIndex) {
            priorityStats[JSONConverter::priorityToString(pair.first)] = pair.second.size();
        }

        // 调度统计
        stats["scheduling_enabled"] = m_enableScheduling;
        stats["scheduled_tasks"] = m_scheduleIndex.size();

        // 执行统计
        if (!m_executionStats.empty()) {
            uint32_t totalExecutions = 0;
            uint32_t maxExecutions = 0;
            for (const auto& pair : m_executionStats) {
                totalExecutions += pair.second;
                maxExecutions = std::max(maxExecutions, pair.second);
            }
            stats["total_executions"] = totalExecutions;
            stats["max_executions"] = maxExecutions;
            stats["average_executions"] = (float)totalExecutions / m_executionStats.size();
        }

        return stats;
    }

protected:
    // ==================== Repository基类实现 ====================

    TaskID getItemId(const TaskData& item) override {
        return item.id;
    }

    JsonObject itemToJson(const TaskData& item, JsonDocument& doc) override {
        return JSONConverter::taskToJson(item, doc);
    }

    Result<TaskData> itemFromJson(const JsonObject& json) override {
        return JSONConverter::taskFromJson(json);
    }

    /**
     * 更新索引
     * @param item 任务数据
     */
    void updateIndexes(const TaskData& item) override {
        TaskID id = item.id;

        // 更新状态索引
        auto& statusList = m_statusIndex[item.status];
        if (std::find(statusList.begin(), statusList.end(), id) == statusList.end()) {
            statusList.push_back(id);
        }

        // 更新类型索引
        auto& typeList = m_typeIndex[item.type];
        if (std::find(typeList.begin(), typeList.end(), id) == typeList.end()) {
            typeList.push_back(id);
        }

        // 更新优先级索引
        auto& priorityList = m_priorityIndex[item.priority];
        if (std::find(priorityList.begin(), priorityList.end(), id) == priorityList.end()) {
            priorityList.push_back(id);
        }

        // 更新调度索引
        if (m_enableScheduling && item.nextExecution > 0) {
            updateScheduleIndex(id, item.nextExecution);
        }

        // 更新执行统计
        if (m_executionStats.find(id) == m_executionStats.end()) {
            m_executionStats[id] = item.executionCount;
        }
    }

    /**
     * 从索引中移除
     * @param item 任务数据
     */
    void removeFromIndexes(const TaskData& item) override {
        TaskID id = item.id;

        // 从状态索引移除
        auto statusIt = m_statusIndex.find(item.status);
        if (statusIt != m_statusIndex.end()) {
            auto& list = statusIt->second;
            list.erase(std::remove(list.begin(), list.end(), id), list.end());
            if (list.empty()) {
                m_statusIndex.erase(statusIt);
            }
        }

        // 从类型索引移除
        auto typeIt = m_typeIndex.find(item.type);
        if (typeIt != m_typeIndex.end()) {
            auto& list = typeIt->second;
            list.erase(std::remove(list.begin(), list.end(), id), list.end());
            if (list.empty()) {
                m_typeIndex.erase(typeIt);
            }
        }

        // 从优先级索引移除
        auto priorityIt = m_priorityIndex.find(item.priority);
        if (priorityIt != m_priorityIndex.end()) {
            auto& list = priorityIt->second;
            list.erase(std::remove(list.begin(), list.end(), id), list.end());
            if (list.empty()) {
                m_priorityIndex.erase(priorityIt);
            }
        }

        // 从调度索引移除
        removeFromScheduleIndex(id);

        // 从执行统计移除
        m_executionStats.erase(id);
    }

private:
    // ==================== 内部辅助方法 ====================

    /**
     * 更新状态索引
     * @param taskId 任务ID
     * @param oldStatus 旧状态
     * @param newStatus 新状态
     */
    void updateStatusIndex(TaskID taskId, TaskStatus oldStatus, TaskStatus newStatus) {
        // 从旧状态索引移除
        auto oldIt = m_statusIndex.find(oldStatus);
        if (oldIt != m_statusIndex.end()) {
            auto& list = oldIt->second;
            list.erase(std::remove(list.begin(), list.end(), taskId), list.end());
            if (list.empty()) {
                m_statusIndex.erase(oldIt);
            }
        }

        // 添加到新状态索引
        auto& newList = m_statusIndex[newStatus];
        if (std::find(newList.begin(), newList.end(), taskId) == newList.end()) {
            newList.push_back(taskId);
        }
    }

    /**
     * 更新调度索引
     * @param taskId 任务ID
     * @param nextExecution 下次执行时间
     */
    void updateScheduleIndex(TaskID taskId, Timestamp nextExecution) {
        // 先移除旧的调度记录
        removeFromScheduleIndex(taskId);

        // 添加新的调度记录
        if (nextExecution > 0) {
            m_scheduleIndex.emplace(nextExecution, taskId);
        }
    }

    /**
     * 从调度索引中移除
     * @param taskId 任务ID
     */
    void removeFromScheduleIndex(TaskID taskId) {
        auto it = m_scheduleIndex.begin();
        while (it != m_scheduleIndex.end()) {
            if (it->second == taskId) {
                it = m_scheduleIndex.erase(it);
            } else {
                ++it;
            }
        }
    }

public:
    // ==================== 高级功能 ====================

    /**
     * 批量更新任务状态
     * @param taskIds 任务ID列表
     * @param newStatus 新状态
     * @return 成功更新的数量
     */
    size_t updateBatchStatus(const std::vector<TaskID>& taskIds, TaskStatus newStatus) {
        beginTransaction();

        size_t successCount = 0;
        for (TaskID id : taskIds) {
            if (updateTaskStatus(id, newStatus, true)) {
                successCount++;
            } else {
                rollbackTransaction();
                return 0;
            }
        }

        if (commitTransaction()) {
            return successCount;
        } else {
            return 0;
        }
    }

    /**
     * 获取任务依赖关系
     * @param taskId 任务ID
     * @return 依赖的任务ID列表
     */
    std::vector<TaskID> getTaskDependencies(TaskID taskId) {
        std::lock_guard<std::mutex> lock(m_mutex);

        auto it = m_cache.find(taskId);
        if (it != m_cache.end()) {
            // 返回任务中包含的信号ID作为依赖
            std::vector<TaskID> dependencies;
            for (SignalID signalId : it->second.signals) {
                dependencies.push_back(static_cast<TaskID>(signalId));
            }
            return dependencies;
        }

        return {};
    }

    /**
     * 验证任务数据完整性
     * @return 验证结果
     */
    struct TaskValidationResult {
        bool isValid;
        std::vector<String> errors;
        std::vector<String> warnings;
        size_t totalTasks;
        size_t validTasks;
        size_t invalidTasks;
    };

    TaskValidationResult validateAllTasks() {
        std::lock_guard<std::mutex> lock(m_mutex);

        TaskValidationResult result;
        result.isValid = true;
        result.totalTasks = m_cache.size();
        result.validTasks = 0;
        result.invalidTasks = 0;

        for (const auto& pair : m_cache) {
            const TaskData& task = pair.second;

            // 基础验证
            if (!task.isValid()) {
                result.invalidTasks++;
                result.errors.push_back("Task " + String(task.id) + " is invalid");
                result.isValid = false;
                continue;
            }

            // 名称验证
            if (task.name.isEmpty()) {
                result.warnings.push_back("Task " + String(task.id) + " has empty name");
            }

            // 信号列表验证
            if (task.signals.empty() && task.type != TaskType::CONDITIONAL) {
                result.warnings.push_back("Task " + String(task.id) + " has no signals");
            }

            // 调度验证
            if (task.type == TaskType::REPEATED && task.interval == 0) {
                result.errors.push_back("Task " + String(task.id) + " is repeated but has no interval");
                result.invalidTasks++;
                result.isValid = false;
                continue;
            }

            result.validTasks++;
        }

        return result;
    }
};
