#pragma once

#include "Repository.h"
#include "../core/DataStructures.h"
#include "../core/JSONConverter.h"
#include <ctime>

/**
 * ESP32-S3 红外控制系统 - 定时器数据仓库
 * 
 * 功能：
 * 1. 定时器数据的CRUD操作
 * 2. 定时触发检查和管理
 * 3. 星期模式和一次性定时器
 * 4. 定时器状态监控
 * 5. 时间计算和验证
 * 
 * 设计原则：
 * - 继承Repository基类
 * - 专门针对TimerData优化
 * - 支持复杂时间调度
 * - 自动维护触发索引
 * - 时区和夏令时支持
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 */

class TimerRepository : public Repository<TimerData, TimerID> {
private:
    // 专用索引
    std::map<TaskID, std::vector<TimerID>> m_taskIndex;        // 按关联任务索引
    std::map<bool, std::vector<TimerID>> m_enabledIndex;       // 按启用状态索引
    std::map<bool, std::vector<TimerID>> m_oneTimeIndex;       // 按一次性标志索引
    
    // 触发索引 - 按下次触发时间排序
    std::multimap<Timestamp, TimerID> m_triggerIndex;
    
    // 星期索引 - 按星期几分组
    std::map<uint8_t, std::vector<TimerID>> m_weekdayIndex;    // 0=周日, 1=周一, ...
    
    // 时间索引 - 按小时分组便于快速查找
    std::map<uint8_t, std::vector<TimerID>> m_hourIndex;
    
    // 配置
    bool m_enableTimeValidation;
    int8_t m_timezoneOffset;        // 时区偏移（小时）
    size_t m_maxTimers;

public:
    /**
     * 构造函数
     * @param enableTimeValidation 是否启用时间验证
     * @param timezoneOffset 时区偏移（小时）
     * @param maxTimers 最大定时器数量
     */
    TimerRepository(bool enableTimeValidation = true, int8_t timezoneOffset = 8, size_t maxTimers = 500)
        : Repository("/timers", ".json", maxTimers, true),
          m_enableTimeValidation(enableTimeValidation),
          m_timezoneOffset(timezoneOffset),
          m_maxTimers(maxTimers) {}
    
    /**
     * 析构函数
     */
    virtual ~TimerRepository() = default;
    
    // ==================== 专用查询方法 ====================
    
    /**
     * 根据关联任务查找定时器
     * @param taskId 任务ID
     * @return 定时器列表
     */
    std::vector<TimerData> findByTask(TaskID taskId) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<TimerData> result;
        auto it = m_taskIndex.find(taskId);
        
        if (it != m_taskIndex.end()) {
            result.reserve(it->second.size());
            for (TimerID id : it->second) {
                auto cacheIt = m_cache.find(id);
                if (cacheIt != m_cache.end()) {
                    result.push_back(cacheIt->second);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 获取启用的定时器
     * @return 定时器列表
     */
    std::vector<TimerData> getEnabledTimers() {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<TimerData> result;
        auto it = m_enabledIndex.find(true);
        
        if (it != m_enabledIndex.end()) {
            result.reserve(it->second.size());
            for (TimerID id : it->second) {
                auto cacheIt = m_cache.find(id);
                if (cacheIt != m_cache.end()) {
                    result.push_back(cacheIt->second);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 获取一次性定时器
     * @return 定时器列表
     */
    std::vector<TimerData> getOneTimeTimers() {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<TimerData> result;
        auto it = m_oneTimeIndex.find(true);
        
        if (it != m_oneTimeIndex.end()) {
            result.reserve(it->second.size());
            for (TimerID id : it->second) {
                auto cacheIt = m_cache.find(id);
                if (cacheIt != m_cache.end()) {
                    result.push_back(cacheIt->second);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 根据星期几查找定时器
     * @param weekday 星期几 (0=周日, 1=周一, ..., 6=周六)
     * @return 定时器列表
     */
    std::vector<TimerData> findByWeekday(uint8_t weekday) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<TimerData> result;
        
        if (weekday > 6) {
            return result; // 无效的星期值
        }
        
        uint8_t weekdayMask = 1 << weekday;
        
        for (const auto& pair : m_cache) {
            const TimerData& timer = pair.second;
            if (timer.enabled && (timer.weekdays & weekdayMask)) {
                result.push_back(timer);
            }
        }
        
        return result;
    }
    
    /**
     * 根据时间范围查找定时器
     * @param startHour 开始小时
     * @param endHour 结束小时
     * @return 定时器列表
     */
    std::vector<TimerData> findByTimeRange(uint8_t startHour, uint8_t endHour) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<TimerData> result;
        
        if (startHour > 23 || endHour > 23) {
            return result; // 无效的小时值
        }
        
        for (const auto& pair : m_cache) {
            const TimerData& timer = pair.second;
            if (timer.enabled && timer.hour >= startHour && timer.hour <= endHour) {
                result.push_back(timer);
            }
        }
        
        return result;
    }
    
    /**
     * 获取即将触发的定时器
     * @param currentTime 当前时间
     * @param lookAheadMinutes 提前查看的分钟数
     * @return 定时器列表
     */
    std::vector<TimerData> getUpcomingTimers(Timestamp currentTime = 0, uint32_t lookAheadMinutes = 60) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (currentTime == 0) {
            currentTime = millis();
        }
        
        Timestamp endTime = currentTime + (lookAheadMinutes * 60 * 1000);
        std::vector<TimerData> result;
        
        // 从触发索引中查找即将触发的定时器
        auto startIt = m_triggerIndex.lower_bound(currentTime);
        auto endIt = m_triggerIndex.upper_bound(endTime);
        
        for (auto it = startIt; it != endIt; ++it) {
            auto cacheIt = m_cache.find(it->second);
            if (cacheIt != m_cache.end() && cacheIt->second.enabled) {
                result.push_back(cacheIt->second);
            }
        }
        
        // 按触发时间排序
        std::sort(result.begin(), result.end(), [](const TimerData& a, const TimerData& b) {
            return a.nextTrigger < b.nextTrigger;
        });
        
        return result;
    }
    
    /**
     * 检查是否有定时器需要触发
     * @param currentTime 当前时间
     * @return 需要触发的定时器列表
     */
    std::vector<TimerData> checkTriggeredTimers(Timestamp currentTime = 0) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (currentTime == 0) {
            currentTime = millis();
        }
        
        std::vector<TimerData> triggered;
        
        // 从触发索引中查找已到期的定时器
        auto endIt = m_triggerIndex.upper_bound(currentTime);
        
        for (auto it = m_triggerIndex.begin(); it != endIt; ++it) {
            auto cacheIt = m_cache.find(it->second);
            if (cacheIt != m_cache.end()) {
                TimerData& timer = cacheIt->second;
                if (timer.enabled && timer.shouldTrigger()) {
                    triggered.push_back(timer);
                    
                    // 更新触发时间
                    timer.lastTriggered = currentTime;
                    
                    // 如果是一次性定时器，禁用它
                    if (timer.oneTime) {
                        timer.enabled = false;
                        updateEnabledIndex(timer.id, true, false);
                    } else {
                        // 计算下次触发时间
                        timer.nextTrigger = timer.calculateNextTrigger();
                        updateTriggerIndex(timer.id, timer.nextTrigger);
                    }
                    
                    // 自动保存
                    if (m_autoSave) {
                        saveItem(timer);
                    }
                }
            }
        }
        
        return triggered;
    }
    
    // ==================== 定时器管理 ====================
    
    /**
     * 启用/禁用定时器
     * @param timerId 定时器ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    bool setTimerEnabled(TimerID timerId, bool enabled) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto it = m_cache.find(timerId);
        if (it == m_cache.end()) {
            return false;
        }
        
        TimerData& timer = it->second;
        bool oldEnabled = timer.enabled;
        
        timer.enabled = enabled;
        timer.modifiedTime = millis();
        
        // 更新启用状态索引
        updateEnabledIndex(timerId, oldEnabled, enabled);
        
        // 如果启用，重新计算触发时间
        if (enabled && !oldEnabled) {
            timer.nextTrigger = timer.calculateNextTrigger();
            updateTriggerIndex(timerId, timer.nextTrigger);
        } else if (!enabled && oldEnabled) {
            // 如果禁用，从触发索引移除
            removeTriggerIndex(timerId);
        }
        
        // 自动保存
        if (m_autoSave) {
            saveItem(timer);
        }
        
        return true;
    }
    
    /**
     * 手动触发定时器
     * @param timerId 定时器ID
     * @return 是否成功
     */
    bool triggerTimer(TimerID timerId) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto it = m_cache.find(timerId);
        if (it == m_cache.end()) {
            return false;
        }
        
        TimerData& timer = it->second;
        timer.lastTriggered = millis();
        
        // 如果是一次性定时器，禁用它
        if (timer.oneTime) {
            timer.enabled = false;
            updateEnabledIndex(timerId, true, false);
        } else {
            // 计算下次触发时间
            timer.nextTrigger = timer.calculateNextTrigger();
            updateTriggerIndex(timerId, timer.nextTrigger);
        }
        
        // 自动保存
        if (m_autoSave) {
            saveItem(timer);
        }
        
        return true;
    }
    
    /**
     * 重置定时器（重新计算下次触发时间）
     * @param timerId 定时器ID
     * @return 是否成功
     */
    bool resetTimer(TimerID timerId) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto it = m_cache.find(timerId);
        if (it == m_cache.end()) {
            return false;
        }
        
        TimerData& timer = it->second;
        timer.nextTrigger = timer.calculateNextTrigger();
        timer.modifiedTime = millis();
        
        // 更新触发索引
        updateTriggerIndex(timerId, timer.nextTrigger);
        
        // 自动保存
        if (m_autoSave) {
            saveItem(timer);
        }
        
        return true;
    }
    
    /**
     * 批量重置所有定时器
     * @return 重置的定时器数量
     */
    size_t resetAllTimers() {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        size_t resetCount = 0;
        
        for (auto& pair : m_cache) {
            TimerData& timer = pair.second;
            if (timer.enabled) {
                timer.nextTrigger = timer.calculateNextTrigger();
                timer.modifiedTime = millis();
                updateTriggerIndex(timer.id, timer.nextTrigger);
                
                if (m_autoSave) {
                    saveItem(timer);
                }
                
                resetCount++;
            }
        }
        
        return resetCount;
    }

    // ==================== 时间计算和验证 ====================

    /**
     * 验证定时器时间设置
     * @param timer 定时器数据
     * @return 验证结果
     */
    struct TimeValidationResult {
        bool isValid;
        std::vector<String> errors;
        std::vector<String> warnings;
    };

    TimeValidationResult validateTimerTime(const TimerData& timer) {
        TimeValidationResult result;
        result.isValid = true;

        // 小时验证
        if (timer.hour > 23) {
            result.errors.push_back("Invalid hour: " + String(timer.hour));
            result.isValid = false;
        }

        // 分钟验证
        if (timer.minute > 59) {
            result.errors.push_back("Invalid minute: " + String(timer.minute));
            result.isValid = false;
        }

        // 星期掩码验证
        if (timer.weekdays > 127) { // 7位掩码最大值
            result.errors.push_back("Invalid weekdays mask: " + String(timer.weekdays));
            result.isValid = false;
        }

        if (timer.weekdays == 0 && !timer.oneTime) {
            result.warnings.push_back("No weekdays selected for recurring timer");
        }

        // 任务ID验证
        if (timer.taskId == INVALID_ID) {
            result.errors.push_back("Invalid task ID");
            result.isValid = false;
        }

        return result;
    }

    /**
     * 获取当前系统时间（考虑时区）
     * @return 当前时间结构
     */
    struct SystemTime {
        uint8_t hour;
        uint8_t minute;
        uint8_t second;
        uint8_t weekday;    // 0=周日, 1=周一, ..., 6=周六
        uint16_t year;
        uint8_t month;
        uint8_t day;
    };

    SystemTime getCurrentTime() {
        SystemTime sysTime;

        // 获取当前时间戳（毫秒）
        Timestamp currentMs = millis();

        // 转换为秒
        time_t currentSec = currentMs / 1000;

        // 应用时区偏移
        currentSec += m_timezoneOffset * 3600;

        // 转换为本地时间结构
        struct tm* timeInfo = gmtime(&currentSec);

        if (timeInfo) {
            sysTime.hour = timeInfo->tm_hour;
            sysTime.minute = timeInfo->tm_min;
            sysTime.second = timeInfo->tm_sec;
            sysTime.weekday = timeInfo->tm_wday;
            sysTime.year = timeInfo->tm_year + 1900;
            sysTime.month = timeInfo->tm_mon + 1;
            sysTime.day = timeInfo->tm_mday;
        } else {
            // 如果时间转换失败，使用默认值
            memset(&sysTime, 0, sizeof(sysTime));
        }

        return sysTime;
    }

    /**
     * 计算两个时间点之间的分钟差
     * @param hour1 第一个时间的小时
     * @param minute1 第一个时间的分钟
     * @param hour2 第二个时间的小时
     * @param minute2 第二个时间的分钟
     * @return 分钟差（可能为负数）
     */
    int32_t calculateMinutesDifference(uint8_t hour1, uint8_t minute1, uint8_t hour2, uint8_t minute2) {
        int32_t minutes1 = hour1 * 60 + minute1;
        int32_t minutes2 = hour2 * 60 + minute2;
        return minutes2 - minutes1;
    }

    // ==================== 统计和监控 ====================

    /**
     * 获取定时器统计信息
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getDetailedStatistics(JsonDocument& doc) override {
        std::lock_guard<std::mutex> lock(m_mutex);

        JsonObject stats = Repository::getStatistics(doc);

        // 基础统计
        stats["timezone_offset"] = m_timezoneOffset;
        stats["time_validation_enabled"] = m_enableTimeValidation;

        // 启用状态统计
        size_t enabledCount = 0;
        size_t disabledCount = 0;
        auto enabledIt = m_enabledIndex.find(true);
        auto disabledIt = m_enabledIndex.find(false);

        if (enabledIt != m_enabledIndex.end()) {
            enabledCount = enabledIt->second.size();
        }
        if (disabledIt != m_enabledIndex.end()) {
            disabledCount = disabledIt->second.size();
        }

        stats["enabled_timers"] = enabledCount;
        stats["disabled_timers"] = disabledCount;

        // 一次性定时器统计
        size_t oneTimeCount = 0;
        size_t recurringCount = 0;
        auto oneTimeIt = m_oneTimeIndex.find(true);
        auto recurringIt = m_oneTimeIndex.find(false);

        if (oneTimeIt != m_oneTimeIndex.end()) {
            oneTimeCount = oneTimeIt->second.size();
        }
        if (recurringIt != m_oneTimeIndex.end()) {
            recurringCount = recurringIt->second.size();
        }

        stats["one_time_timers"] = oneTimeCount;
        stats["recurring_timers"] = recurringCount;

        // 星期分布统计
        JsonObject weekdayStats = stats.createNestedObject("weekday_distribution");
        const char* weekdayNames[] = {"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"};

        for (int i = 0; i < 7; i++) {
            auto it = m_weekdayIndex.find(i);
            size_t count = (it != m_weekdayIndex.end()) ? it->second.size() : 0;
            weekdayStats[weekdayNames[i]] = count;
        }

        // 小时分布统计
        JsonObject hourStats = stats.createNestedObject("hour_distribution");
        for (int i = 0; i < 24; i++) {
            auto it = m_hourIndex.find(i);
            size_t count = (it != m_hourIndex.end()) ? it->second.size() : 0;
            hourStats[String(i)] = count;
        }

        // 触发统计
        stats["scheduled_triggers"] = m_triggerIndex.size();

        // 下次触发时间
        if (!m_triggerIndex.empty()) {
            stats["next_trigger_time"] = m_triggerIndex.begin()->first;
        }

        return stats;
    }

protected:
    // ==================== Repository基类实现 ====================

    TimerID getItemId(const TimerData& item) override {
        return item.id;
    }

    JsonObject itemToJson(const TimerData& item, JsonDocument& doc) override {
        return JSONConverter::timerToJson(item, doc);
    }

    Result<TimerData> itemFromJson(const JsonObject& json) override {
        return JSONConverter::timerFromJson(json);
    }

    /**
     * 更新索引
     * @param item 定时器数据
     */
    void updateIndexes(const TimerData& item) override {
        TimerID id = item.id;

        // 更新任务索引
        auto& taskList = m_taskIndex[item.taskId];
        if (std::find(taskList.begin(), taskList.end(), id) == taskList.end()) {
            taskList.push_back(id);
        }

        // 更新启用状态索引
        auto& enabledList = m_enabledIndex[item.enabled];
        if (std::find(enabledList.begin(), enabledList.end(), id) == enabledList.end()) {
            enabledList.push_back(id);
        }

        // 更新一次性标志索引
        auto& oneTimeList = m_oneTimeIndex[item.oneTime];
        if (std::find(oneTimeList.begin(), oneTimeList.end(), id) == oneTimeList.end()) {
            oneTimeList.push_back(id);
        }

        // 更新星期索引
        for (int i = 0; i < 7; i++) {
            if (item.weekdays & (1 << i)) {
                auto& weekdayList = m_weekdayIndex[i];
                if (std::find(weekdayList.begin(), weekdayList.end(), id) == weekdayList.end()) {
                    weekdayList.push_back(id);
                }
            }
        }

        // 更新小时索引
        auto& hourList = m_hourIndex[item.hour];
        if (std::find(hourList.begin(), hourList.end(), id) == hourList.end()) {
            hourList.push_back(id);
        }

        // 更新触发索引
        if (item.enabled && item.nextTrigger > 0) {
            updateTriggerIndex(id, item.nextTrigger);
        }
    }

    /**
     * 从索引中移除
     * @param item 定时器数据
     */
    void removeFromIndexes(const TimerData& item) override {
        TimerID id = item.id;

        // 从任务索引移除
        auto taskIt = m_taskIndex.find(item.taskId);
        if (taskIt != m_taskIndex.end()) {
            auto& list = taskIt->second;
            list.erase(std::remove(list.begin(), list.end(), id), list.end());
            if (list.empty()) {
                m_taskIndex.erase(taskIt);
            }
        }

        // 从启用状态索引移除
        auto enabledIt = m_enabledIndex.find(item.enabled);
        if (enabledIt != m_enabledIndex.end()) {
            auto& list = enabledIt->second;
            list.erase(std::remove(list.begin(), list.end(), id), list.end());
            if (list.empty()) {
                m_enabledIndex.erase(enabledIt);
            }
        }

        // 从一次性标志索引移除
        auto oneTimeIt = m_oneTimeIndex.find(item.oneTime);
        if (oneTimeIt != m_oneTimeIndex.end()) {
            auto& list = oneTimeIt->second;
            list.erase(std::remove(list.begin(), list.end(), id), list.end());
            if (list.empty()) {
                m_oneTimeIndex.erase(oneTimeIt);
            }
        }

        // 从星期索引移除
        for (int i = 0; i < 7; i++) {
            if (item.weekdays & (1 << i)) {
                auto weekdayIt = m_weekdayIndex.find(i);
                if (weekdayIt != m_weekdayIndex.end()) {
                    auto& list = weekdayIt->second;
                    list.erase(std::remove(list.begin(), list.end(), id), list.end());
                    if (list.empty()) {
                        m_weekdayIndex.erase(weekdayIt);
                    }
                }
            }
        }

        // 从小时索引移除
        auto hourIt = m_hourIndex.find(item.hour);
        if (hourIt != m_hourIndex.end()) {
            auto& list = hourIt->second;
            list.erase(std::remove(list.begin(), list.end(), id), list.end());
            if (list.empty()) {
                m_hourIndex.erase(hourIt);
            }
        }

        // 从触发索引移除
        removeTriggerIndex(id);
    }

private:
    // ==================== 内部辅助方法 ====================

    /**
     * 更新启用状态索引
     * @param timerId 定时器ID
     * @param oldEnabled 旧启用状态
     * @param newEnabled 新启用状态
     */
    void updateEnabledIndex(TimerID timerId, bool oldEnabled, bool newEnabled) {
        // 从旧状态索引移除
        auto oldIt = m_enabledIndex.find(oldEnabled);
        if (oldIt != m_enabledIndex.end()) {
            auto& list = oldIt->second;
            list.erase(std::remove(list.begin(), list.end(), timerId), list.end());
            if (list.empty()) {
                m_enabledIndex.erase(oldIt);
            }
        }

        // 添加到新状态索引
        auto& newList = m_enabledIndex[newEnabled];
        if (std::find(newList.begin(), newList.end(), timerId) == newList.end()) {
            newList.push_back(timerId);
        }
    }

    /**
     * 更新触发索引
     * @param timerId 定时器ID
     * @param nextTrigger 下次触发时间
     */
    void updateTriggerIndex(TimerID timerId, Timestamp nextTrigger) {
        // 先移除旧的触发记录
        removeTriggerIndex(timerId);

        // 添加新的触发记录
        if (nextTrigger > 0) {
            m_triggerIndex.emplace(nextTrigger, timerId);
        }
    }

    /**
     * 从触发索引中移除
     * @param timerId 定时器ID
     */
    void removeTriggerIndex(TimerID timerId) {
        auto it = m_triggerIndex.begin();
        while (it != m_triggerIndex.end()) {
            if (it->second == timerId) {
                it = m_triggerIndex.erase(it);
            } else {
                ++it;
            }
        }
    }

public:
    // ==================== 高级功能 ====================

    /**
     * 清理过期的一次性定时器
     * @return 清理的定时器数量
     */
    size_t cleanupExpiredOneTimeTimers() {
        std::lock_guard<std::mutex> lock(m_mutex);

        std::vector<TimerID> toDelete;

        for (const auto& pair : m_cache) {
            const TimerData& timer = pair.second;
            if (timer.oneTime && !timer.enabled && timer.lastTriggered > 0) {
                toDelete.push_back(pair.first);
            }
        }

        size_t deletedCount = 0;
        for (TimerID id : toDelete) {
            if (deleteById(id)) {
                deletedCount++;
            }
        }

        return deletedCount;
    }

    /**
     * 验证所有定时器
     * @return 验证结果
     */
    struct TimerValidationResult {
        bool isValid;
        std::vector<String> errors;
        std::vector<String> warnings;
        size_t totalTimers;
        size_t validTimers;
        size_t invalidTimers;
    };

    TimerValidationResult validateAllTimers() {
        std::lock_guard<std::mutex> lock(m_mutex);

        TimerValidationResult result;
        result.isValid = true;
        result.totalTimers = m_cache.size();
        result.validTimers = 0;
        result.invalidTimers = 0;

        for (const auto& pair : m_cache) {
            const TimerData& timer = pair.second;

            auto validation = validateTimerTime(timer);
            if (!validation.isValid) {
                result.invalidTimers++;
                result.isValid = false;
                for (const String& error : validation.errors) {
                    result.errors.push_back("Timer " + String(timer.id) + ": " + error);
                }
            } else {
                result.validTimers++;
            }

            for (const String& warning : validation.warnings) {
                result.warnings.push_back("Timer " + String(timer.id) + ": " + warning);
            }
        }

        return result;
    }
};
