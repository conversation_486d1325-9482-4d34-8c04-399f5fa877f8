#include "IRController.h"
#include "../core/JSONConverter.h"

IRController::IRController() : m_irSend(nullptr), m_irRecv(nullptr), m_initialized(false), m_learning(false) {}

IRController::~IRController() {
    cleanup();
}

bool IRController::initialize(const IRConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) return true;
    
    m_config = config;
    
    if (!initializeSender()) {
        Serial.println("❌ IRController: Failed to initialize sender");
        return false;
    }
    
    if (m_config.enableReceiver && !initializeReceiver()) {
        Serial.println("❌ IRController: Failed to initialize receiver");
        return false;
    }
    
    m_initialized = true;
    Serial.println("✅ IRController: Initialized successfully");
    return true;
}

void IRController::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        if (m_learning) stopLearning();
        
        if (m_irSend) {
            delete m_irSend;
            m_irSend = nullptr;
        }
        
        if (m_irRecv) {
            delete m_irRecv;
            m_irRecv = nullptr;
        }
        
        m_initialized = false;
        Serial.println("✅ IRController: Cleanup completed");
    }
}

bool IRController::sendSignal(const SignalData& signal) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !validateSignal(signal)) {
        updateSendStats(false);
        return false;
    }
    
    bool success = false;
    
    if (signal.protocol != IRProtocol::RAW && signal.protocol != IRProtocol::UNKNOWN) {
        success = sendByProtocol(signal.protocol, signal.code, signal.bits, signal.frequency);
    } else if (!signal.rawData.empty()) {
        success = sendRawSignal(signal.rawData, signal.frequency);
    }
    
    updateSendStats(success);
    return success;
}

bool IRController::sendRawSignal(const std::vector<uint16_t>& rawData, uint32_t frequency) {
    if (!m_initialized || !m_irSend || rawData.empty()) return false;
    
    m_irSend->sendRaw(const_cast<uint16_t*>(rawData.data()), rawData.size(), frequency);
    return true;
}

bool IRController::sendProtocolSignal(IRProtocol protocol, uint64_t code, uint8_t bits, uint32_t frequency) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        updateSendStats(false);
        return false;
    }
    
    bool success = sendByProtocol(protocol, code, bits, frequency);
    updateSendStats(success);
    return success;
}

bool IRController::startLearning() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_irRecv) return false;
    
    m_learning = true;
    m_learnedSignal = SignalData();
    m_irRecv->enableIRIn();
    
    Serial.println("✅ IRController: Learning started");
    return true;
}

bool IRController::stopLearning() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_learning) return false;
    
    m_learning = false;
    if (m_irRecv) m_irRecv->disableIRIn();
    
    Serial.println("✅ IRController: Learning stopped");
    return true;
}

Result<SignalData> IRController::getLearnedSignal() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_learning || !m_irRecv) {
        return Result<SignalData>::Error(ErrorCode::INVALID_STATE, "Not in learning mode");
    }
    
    decode_results results;
    if (m_irRecv->decode(&results)) {
        SignalData signal;
        signal.protocol = detectProtocol(results);
        signal.deviceType = guessDeviceType(signal.protocol, results.value);
        signal.code = results.value;
        signal.bits = results.bits;
        signal.frequency = m_config.frequency;
        signal.createdTime = millis();
        signal.modifiedTime = signal.createdTime;
        
        convertToRawData(results, signal.rawData);
        
        m_irRecv->resume();
        updateReceiveStats(true);
        
        return Result<SignalData>::Success(signal);
    }
    
    return Result<SignalData>::Error(ErrorCode::DATA_NOT_FOUND, "No signal received");
}

bool IRController::hasLearnedSignal() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_learning && m_irRecv && m_irRecv->decode();
}

bool IRController::isSending() const {
    return false; // IRremoteESP8266库没有提供发送状态查询
}

bool IRController::isReceiving() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_learning && m_irRecv;
}

bool IRController::updateConfig(const IRConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        cleanup();
        m_config = config;
        return initialize(config);
    } else {
        m_config = config;
        return true;
    }
}

JsonObject IRController::getDetailedStatistics(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    JsonObject stats = doc.createNestedObject("ir_controller_statistics");
    stats["initialized"] = m_initialized;
    stats["learning"] = m_learning;
    stats["send_count"] = m_stats.sendCount;
    stats["receive_count"] = m_stats.receiveCount;
    stats["send_errors"] = m_stats.sendErrors;
    stats["receive_errors"] = m_stats.receiveErrors;
    stats["last_send"] = m_stats.lastSend;
    stats["last_receive"] = m_stats.lastReceive;
    
    JsonObject config = stats.createNestedObject("config");
    config["send_pin"] = m_config.sendPin;
    config["recv_pin"] = m_config.recvPin;
    config["buffer_size"] = m_config.bufferSize;
    config["timeout"] = m_config.timeout;
    config["enable_receiver"] = m_config.enableReceiver;
    config["frequency"] = m_config.frequency;
    
    return stats;
}

bool IRController::testSender() {
    if (!m_initialized || !m_irSend) return false;
    
    // 发送测试信号（NEC协议的电源键）
    return sendProtocolSignal(IRProtocol::NEC, 0xFF00FF, 32, 38000);
}

bool IRController::testReceiver() {
    if (!m_initialized || !m_irRecv) return false;
    
    // 启动接收测试
    m_irRecv->enableIRIn();
    delay(100);
    
    bool hasSignal = m_irRecv->decode();
    m_irRecv->disableIRIn();
    
    return hasSignal;
}

bool IRController::performSelfTest() {
    bool senderOk = testSender();
    bool receiverOk = m_config.enableReceiver ? testReceiver() : true;
    
    Serial.printf("IRController Self Test - Sender: %s, Receiver: %s\n",
                  senderOk ? "OK" : "FAIL", receiverOk ? "OK" : "FAIL");
    
    return senderOk && receiverOk;
}
