#include "IRController.h"
#include "../core/IDGenerator.h"
#include <Arduino.h>

IRController::IRController(const HardwareConfig& config)
    : m_irSend(nullptr), m_irRecv(nullptr), m_config(config),
      m_operationState(OperationState::IDLE), m_learningState(LearningState::STOPPED),
      m_initialized(false), m_hardwareError(false), m_queueProcessing(false),
      m_learningActive(false), m_learningStartTime(0), m_learningTimeout(30000),
      m_totalSent(0), m_totalReceived(0), m_sendErrors(0), m_receiveErrors(0),
      m_learningAttempts(0), m_learningSuccesses(0) {
}

IRController::~IRController() {
    cleanup();
}

bool IRController::initialize() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        return true;
    }
    
    // 验证配置
    if (m_config.sendPin == m_config.recvPin) {
        Serial.println("❌ IRController: Send and receive pins cannot be the same");
        return false;
    }
    
    // 初始化发送器
    if (m_config.enableSender && !initializeSender()) {
        Serial.println("❌ IRController: Failed to initialize sender");
        return false;
    }
    
    // 初始化接收器
    if (m_config.enableReceiver && !initializeReceiver()) {
        Serial.println("❌ IRController: Failed to initialize receiver");
        cleanup();
        return false;
    }
    
    m_initialized = true;
    m_hardwareError = false;
    setOperationState(OperationState::IDLE);
    
    Serial.printf("✅ IRController: Initialized - Send:%d, Recv:%d, Freq:%dHz\n",
                 m_config.sendPin, m_config.recvPin, m_config.frequency);
    
    return true;
}

void IRController::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        // 停止所有操作
        stopLearning();
        stopSending();
        
        // 清理硬件对象
        if (m_irSend) {
            delete m_irSend;
            m_irSend = nullptr;
        }
        
        if (m_irRecv) {
            m_irRecv->disableIRIn();
            delete m_irRecv;
            m_irRecv = nullptr;
        }
        
        // 清理队列
        while (!m_sendQueue.empty()) {
            m_sendQueue.pop();
        }
        
        m_initialized = false;
        setOperationState(OperationState::IDLE);
        
        Serial.println("✅ IRController: Cleanup completed");
    }
}

bool IRController::sendSignal(const SignalData& signal, std::function<void(bool)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_config.enableSender) {
        if (callback) callback(false);
        return false;
    }
    
    if (!validateSignalData(signal)) {
        recordError("sendSignal", "Invalid signal data");
        if (callback) callback(false);
        return false;
    }
    
    // 添加到发送队列
    m_sendQueue.emplace(signal, callback, 0);
    
    return true;
}

bool IRController::sendSignalBatch(const std::vector<SignalData>& signals, std::function<void(bool)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_config.enableSender) {
        if (callback) callback(false);
        return false;
    }
    
    if (signals.empty()) {
        if (callback) callback(true);
        return true;
    }
    
    // 验证所有信号
    for (const auto& signal : signals) {
        if (!validateSignalData(signal)) {
            recordError("sendSignalBatch", "Invalid signal in batch");
            if (callback) callback(false);
            return false;
        }
    }
    
    // 添加所有信号到队列
    for (const auto& signal : signals) {
        m_sendQueue.emplace(signal, nullptr, 0);
    }
    
    // 最后一个信号设置回调
    if (!m_sendQueue.empty() && callback) {
        // 需要修改队列中最后一个项目的回调
        // 这里简化处理，直接调用回调
        callback(true);
    }
    
    return true;
}

bool IRController::sendSignalPriority(const SignalData& signal, std::function<void(bool)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_config.enableSender) {
        if (callback) callback(false);
        return false;
    }
    
    if (!validateSignalData(signal)) {
        recordError("sendSignalPriority", "Invalid signal data");
        if (callback) callback(false);
        return false;
    }
    
    // 创建临时队列，将优先信号放在前面
    std::queue<SendQueueItem> tempQueue;
    tempQueue.emplace(signal, callback, 1); // 高优先级
    
    // 将原队列内容移到临时队列后面
    while (!m_sendQueue.empty()) {
        tempQueue.push(m_sendQueue.front());
        m_sendQueue.pop();
    }
    
    // 替换原队列
    m_sendQueue = std::move(tempQueue);
    
    return true;
}

bool IRController::stopSending() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 清空发送队列
    while (!m_sendQueue.empty()) {
        auto& item = m_sendQueue.front();
        if (item.callback) {
            item.callback(false); // 通知取消
        }
        m_sendQueue.pop();
    }
    
    m_queueProcessing = false;
    
    if (m_operationState == OperationState::SENDING) {
        setOperationState(OperationState::IDLE);
    }
    
    return true;
}

size_t IRController::getSendQueueSize() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_sendQueue.size();
}

bool IRController::enableReceiver() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_irRecv) {
        return false;
    }
    
    m_irRecv->enableIRIn();
    m_config.enableReceiver = true;
    
    Serial.println("✅ IRController: Receiver enabled");
    return true;
}

bool IRController::disableReceiver() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_irRecv) {
        return false;
    }
    
    m_irRecv->disableIRIn();
    m_config.enableReceiver = false;
    
    Serial.println("✅ IRController: Receiver disabled");
    return true;
}

bool IRController::hasNewSignal() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_config.enableReceiver || !m_irRecv) {
        return false;
    }
    
    return m_irRecv->decode(&m_decodeResults);
}

Result<SignalData> IRController::getReceivedSignal() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_config.enableReceiver || !m_irRecv) {
        return Result<SignalData>::Error(ErrorCode::SYSTEM_HARDWARE_ERROR, "Receiver not available");
    }
    
    if (!m_irRecv->decode(&m_decodeResults)) {
        return Result<SignalData>::Error(ErrorCode::RESOURCE_NOT_FOUND, "No signal received");
    }
    
    // 转换接收到的信号
    SignalData signal;
    signal.id = GENERATE_SIGNAL_ID();
    signal.protocol = convertLibTypeToProtocol(m_decodeResults.decode_type);
    signal.code = m_decodeResults.value;
    signal.bits = m_decodeResults.bits;
    signal.frequency = m_config.frequency;
    signal.createdTime = millis();
    signal.modifiedTime = signal.createdTime;
    
    // 复制原始数据
    if (m_decodeResults.rawlen > 0) {
        signal.rawData.clear();
        signal.rawData.reserve(m_decodeResults.rawlen);
        for (uint16_t i = 0; i < m_decodeResults.rawlen; i++) {
            signal.rawData.push_back(m_decodeResults.rawbuf[i]);
        }
    }
    
    // 恢复接收器
    m_irRecv->resume();
    
    m_totalReceived++;
    
    return Result<SignalData>::Success(signal);
}

void IRController::setSignalReceivedCallback(std::function<void(const SignalData&)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_signalReceivedCallback = callback;
}

bool IRController::startLearning(uint32_t timeout, std::function<void(const LearningResult&)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized || !m_config.enableReceiver || !m_irRecv) {
        return false;
    }
    
    if (m_learningActive) {
        return false; // 已在学习中
    }
    
    m_learningActive = true;
    m_learningStartTime = millis();
    m_learningTimeout = timeout;
    m_learningCompleteCallback = callback;
    m_learningAttempts++;
    
    setLearningState(LearningState::WAITING);
    setOperationState(OperationState::LEARNING);
    
    // 确保接收器启用
    m_irRecv->enableIRIn();
    
    Serial.printf("✅ IRController: Learning started - timeout: %dms\n", timeout);
    
    return true;
}

bool IRController::stopLearning() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_learningActive) {
        return true;
    }
    
    m_learningActive = false;
    setLearningState(LearningState::STOPPED);
    
    if (m_operationState == OperationState::LEARNING) {
        setOperationState(OperationState::IDLE);
    }
    
    // 通知学习完成
    if (m_learningCompleteCallback) {
        LearningResult result;
        result.success = false;
        result.state = LearningState::STOPPED;
        result.duration = millis() - m_learningStartTime;
        result.errorMessage = "Learning stopped by user";
        
        m_learningCompleteCallback(result);
        m_learningCompleteCallback = nullptr;
    }
    
    Serial.println("✅ IRController: Learning stopped");
    
    return true;
}

IRController::LearningState IRController::getLearningState() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_learningState;
}

uint8_t IRController::getLearningProgress() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_learningActive) {
        return 0;
    }
    
    uint32_t elapsed = millis() - m_learningStartTime;
    if (elapsed >= m_learningTimeout) {
        return 100;
    }
    
    return (elapsed * 100) / m_learningTimeout;
}

Result<SignalData> IRController::saveLearningResult(const String& name, const String& description, DeviceType deviceType) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_learningState != LearningState::RECEIVED) {
        return Result<SignalData>::Error(ErrorCode::INVALID_PARAMETER, "No signal learned");
    }
    
    // 设置信号信息
    m_learnedSignal.name = name;
    m_learnedSignal.description = description;
    m_learnedSignal.deviceType = deviceType;
    m_learnedSignal.modifiedTime = millis();
    
    SignalData result = m_learnedSignal;
    
    // 重置学习状态
    m_learningActive = false;
    setLearningState(LearningState::STOPPED);
    setOperationState(OperationState::IDLE);
    
    m_learningSuccesses++;
    
    Serial.printf("✅ IRController: Signal saved - %s\n", name.c_str());
    
    return Result<SignalData>::Success(result);
}

void IRController::setLearningCompleteCallback(std::function<void(const LearningResult&)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_learningCompleteCallback = callback;
}

IRController::OperationState IRController::getOperationState() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_operationState;
}

JsonObject IRController::getHardwareStatus(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject status = doc.createNestedObject("hardware_status");

    status["initialized"] = m_initialized;
    status["hardware_error"] = m_hardwareError;
    status["operation_state"] = static_cast<int>(m_operationState);
    status["learning_state"] = static_cast<int>(m_learningState);

    // 配置信息
    JsonObject config = status.createNestedObject("config");
    config["send_pin"] = m_config.sendPin;
    config["recv_pin"] = m_config.recvPin;
    config["frequency"] = m_config.frequency;
    config["buffer_size"] = m_config.bufferSize;
    config["timeout"] = m_config.timeout;
    config["enable_receiver"] = m_config.enableReceiver;
    config["enable_sender"] = m_config.enableSender;
    config["duty_cycle"] = m_config.dutyCycle;

    // 队列状态
    status["send_queue_size"] = m_sendQueue.size();
    status["queue_processing"] = m_queueProcessing;

    // 学习状态
    status["learning_active"] = m_learningActive;
    if (m_learningActive) {
        status["learning_progress"] = getLearningProgress();
        status["learning_elapsed"] = millis() - m_learningStartTime;
    }

    return status;
}

JsonObject IRController::getStatistics(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject stats = doc.createNestedObject("ir_statistics");

    stats["total_sent"] = m_totalSent;
    stats["total_received"] = m_totalReceived;
    stats["send_errors"] = m_sendErrors;
    stats["receive_errors"] = m_receiveErrors;
    stats["learning_attempts"] = m_learningAttempts;
    stats["learning_successes"] = m_learningSuccesses;

    // 计算成功率
    if (m_totalSent > 0) {
        stats["send_success_rate"] = (float)(m_totalSent - m_sendErrors) / m_totalSent;
    }

    if (m_learningAttempts > 0) {
        stats["learning_success_rate"] = (float)m_learningSuccesses / m_learningAttempts;
    }

    return stats;
}

void IRController::setStateChangeCallback(std::function<void(OperationState, OperationState)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_stateChangeCallback = callback;
}

void IRController::resetStatistics() {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_totalSent = 0;
    m_totalReceived = 0;
    m_sendErrors = 0;
    m_receiveErrors = 0;
    m_learningAttempts = 0;
    m_learningSuccesses = 0;

    Serial.println("✅ IRController: Statistics reset");
}

bool IRController::updateConfig(const HardwareConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 验证新配置
    if (config.sendPin == config.recvPin) {
        Serial.println("❌ IRController: Send and receive pins cannot be the same");
        return false;
    }

    if (config.frequency < 30000 || config.frequency > 60000) {
        Serial.println("❌ IRController: Invalid frequency range");
        return false;
    }

    // 如果已初始化且关键配置改变，需要重新初始化
    bool needReinit = m_initialized && (
        config.sendPin != m_config.sendPin ||
        config.recvPin != m_config.recvPin ||
        config.bufferSize != m_config.bufferSize
    );

    m_config = config;

    if (needReinit) {
        cleanup();
        return initialize();
    }

    // 更新频率等可以动态改变的配置
    if (m_irSend) {
        // IRsend库通常在发送时指定频率，这里记录配置即可
    }

    Serial.println("✅ IRController: Configuration updated");
    return true;
}

bool IRController::testHardware() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) {
        return false;
    }

    bool testResult = true;

    // 测试发送器
    if (m_config.enableSender && m_irSend) {
        // 发送一个测试信号（NEC协议的测试码）
        try {
            m_irSend->sendNEC(0x00FF00FF, 32);
            Serial.println("✅ IRController: Sender test passed");
        } catch (...) {
            Serial.println("❌ IRController: Sender test failed");
            testResult = false;
        }
    }

    // 测试接收器
    if (m_config.enableReceiver && m_irRecv) {
        // 检查接收器是否响应
        bool wasEnabled = m_config.enableReceiver;
        disableReceiver();
        delay(10);
        enableReceiver();

        if (wasEnabled != m_config.enableReceiver) {
            Serial.println("❌ IRController: Receiver test failed");
            testResult = false;
        } else {
            Serial.println("✅ IRController: Receiver test passed");
        }
    }

    return testResult;
}

void IRController::loop() {
    if (!m_initialized) {
        return;
    }

    // 处理发送队列
    processSendQueue();

    // 处理接收信号
    if (m_config.enableReceiver) {
        processReceivedSignal();
    }

    // 处理学习过程
    if (m_learningActive) {
        processLearning();
    }
}

// ==================== 内部实现方法 ====================

bool IRController::initializeSender() {
    try {
        m_irSend = new IRsend(m_config.sendPin);
        m_irSend->begin();

        Serial.printf("✅ IRController: Sender initialized on pin %d\n", m_config.sendPin);
        return true;
    } catch (...) {
        Serial.printf("❌ IRController: Failed to initialize sender on pin %d\n", m_config.sendPin);
        if (m_irSend) {
            delete m_irSend;
            m_irSend = nullptr;
        }
        return false;
    }
}

bool IRController::initializeReceiver() {
    try {
        m_irRecv = new IRrecv(m_config.recvPin, m_config.bufferSize, m_config.timeout);
        m_irRecv->enableIRIn();

        Serial.printf("✅ IRController: Receiver initialized on pin %d\n", m_config.recvPin);
        return true;
    } catch (...) {
        Serial.printf("❌ IRController: Failed to initialize receiver on pin %d\n", m_config.recvPin);
        if (m_irRecv) {
            delete m_irRecv;
            m_irRecv = nullptr;
        }
        return false;
    }
}

void IRController::processSendQueue() {
    if (m_queueProcessing || m_sendQueue.empty() || m_operationState != OperationState::IDLE) {
        return;
    }

    m_queueProcessing = true;
    setOperationState(OperationState::SENDING);

    auto& item = m_sendQueue.front();
    bool success = doSendSignal(item.signal);

    // 调用回调
    if (item.callback) {
        item.callback(success);
    }

    // 移除已处理的项目
    m_sendQueue.pop();

    m_queueProcessing = false;
    setOperationState(OperationState::IDLE);

    if (success) {
        m_totalSent++;
    } else {
        m_sendErrors++;
    }
}

bool IRController::doSendSignal(const SignalData& signal) {
    if (!m_irSend || !validateSignalData(signal)) {
        return false;
    }

    try {
        decode_type_t libType = convertProtocolToLibType(signal.protocol);

        if (signal.protocol == IRProtocol::RAW && !signal.rawData.empty()) {
            // 发送原始数据
            uint16_t* rawData = new uint16_t[signal.rawData.size()];
            for (size_t i = 0; i < signal.rawData.size(); i++) {
                rawData[i] = signal.rawData[i];
            }

            m_irSend->sendRaw(rawData, signal.rawData.size(), signal.frequency);
            delete[] rawData;
        } else {
            // 发送编码信号
            switch (signal.protocol) {
                case IRProtocol::NEC:
                    m_irSend->sendNEC(signal.code, signal.bits);
                    break;
                case IRProtocol::SONY:
                    m_irSend->sendSony(signal.code, signal.bits);
                    break;
                case IRProtocol::LG:
                    m_irSend->sendLG(signal.code, signal.bits);
                    break;
                case IRProtocol::SAMSUNG:
                    m_irSend->sendSAMSUNG(signal.code, signal.bits);
                    break;
                case IRProtocol::PANASONIC:
                    m_irSend->sendPanasonic(signal.code, signal.bits);
                    break;
                case IRProtocol::JVC:
                    m_irSend->sendJVC(signal.code, signal.bits);
                    break;
                case IRProtocol::RC5:
                    m_irSend->sendRC5(signal.code, signal.bits);
                    break;
                case IRProtocol::RC6:
                    m_irSend->sendRC6(signal.code, signal.bits);
                    break;
                default:
                    return false;
            }
        }

        // 发送间隔
        delay(100);

        return true;
    } catch (...) {
        recordError("doSendSignal", "Exception during signal transmission");
        return false;
    }
}

void IRController::processReceivedSignal() {
    if (!m_irRecv || m_operationState == OperationState::SENDING) {
        return;
    }

    if (m_irRecv->decode(&m_decodeResults)) {
        // 转换接收到的信号
        SignalData signal;
        signal.id = GENERATE_SIGNAL_ID();
        signal.protocol = convertLibTypeToProtocol(m_decodeResults.decode_type);
        signal.code = m_decodeResults.value;
        signal.bits = m_decodeResults.bits;
        signal.frequency = m_config.frequency;
        signal.createdTime = millis();
        signal.modifiedTime = signal.createdTime;

        // 复制原始数据
        if (m_decodeResults.rawlen > 0) {
            signal.rawData.clear();
            signal.rawData.reserve(m_decodeResults.rawlen);
            for (uint16_t i = 0; i < m_decodeResults.rawlen; i++) {
                signal.rawData.push_back(m_decodeResults.rawbuf[i]);
            }
        }

        // 恢复接收器
        m_irRecv->resume();

        m_totalReceived++;

        // 如果在学习模式，保存学习结果
        if (m_learningActive && m_learningState == LearningState::WAITING) {
            m_learnedSignal = signal;
            setLearningState(LearningState::RECEIVED);

            // 通知学习完成
            if (m_learningCompleteCallback) {
                LearningResult result;
                result.success = true;
                result.signal = signal;
                result.state = LearningState::RECEIVED;
                result.duration = millis() - m_learningStartTime;

                m_learningCompleteCallback(result);
            }
        }

        // 调用信号接收回调
        if (m_signalReceivedCallback) {
            m_signalReceivedCallback(signal);
        }
    }
}

void IRController::processLearning() {
    if (!m_learningActive) {
        return;
    }

    uint32_t elapsed = millis() - m_learningStartTime;

    // 检查超时
    if (elapsed >= m_learningTimeout) {
        m_learningActive = false;
        setLearningState(LearningState::TIMEOUT);
        setOperationState(OperationState::IDLE);

        // 通知学习超时
        if (m_learningCompleteCallback) {
            LearningResult result;
            result.success = false;
            result.state = LearningState::TIMEOUT;
            result.duration = elapsed;
            result.errorMessage = "Learning timeout";

            m_learningCompleteCallback(result);
            m_learningCompleteCallback = nullptr;
        }

        Serial.println("⚠️ IRController: Learning timeout");
    }
}

decode_type_t IRController::convertProtocolToLibType(IRProtocol protocol) {
    switch (protocol) {
        case IRProtocol::NEC: return NEC;
        case IRProtocol::SONY: return SONY;
        case IRProtocol::LG: return LG;
        case IRProtocol::SAMSUNG: return SAMSUNG;
        case IRProtocol::PANASONIC: return PANASONIC;
        case IRProtocol::JVC: return JVC;
        case IRProtocol::RC5: return RC5;
        case IRProtocol::RC6: return RC6;
        case IRProtocol::RAW: return RAW;
        default: return UNKNOWN;
    }
}

IRProtocol IRController::convertLibTypeToProtocol(decode_type_t libType) {
    switch (libType) {
        case NEC: return IRProtocol::NEC;
        case SONY: return IRProtocol::SONY;
        case LG: return IRProtocol::LG;
        case SAMSUNG: return IRProtocol::SAMSUNG;
        case PANASONIC: return IRProtocol::PANASONIC;
        case JVC: return IRProtocol::JVC;
        case RC5: return IRProtocol::RC5;
        case RC6: return IRProtocol::RC6;
        case RAW: return IRProtocol::RAW;
        default: return IRProtocol::UNKNOWN;
    }
}

void IRController::setOperationState(OperationState newState) {
    OperationState oldState = m_operationState;
    m_operationState = newState;

    if (oldState != newState && m_stateChangeCallback) {
        m_stateChangeCallback(oldState, newState);
    }
}

void IRController::setLearningState(LearningState newState) {
    m_learningState = newState;
}

bool IRController::validateSignalData(const SignalData& signal) {
    // 基础验证
    if (signal.protocol == IRProtocol::UNKNOWN) {
        return false;
    }

    // 频率验证
    if (signal.frequency < 30000 || signal.frequency > 60000) {
        return false;
    }

    // 数据验证
    if (signal.protocol == IRProtocol::RAW) {
        // 原始数据模式，需要有原始数据
        if (signal.rawData.empty()) {
            return false;
        }

        // 检查原始数据长度
        if (signal.rawData.size() > m_config.bufferSize) {
            return false;
        }
    } else {
        // 编码模式，需要有信号码
        if (signal.code == 0) {
            return false;
        }

        // 检查位数
        if (signal.bits == 0 || signal.bits > 64) {
            return false;
        }
    }

    return true;
}

void IRController::recordError(const String& operation, const String& error) {
    Serial.printf("❌ IRController::%s - %s\n", operation.c_str(), error.c_str());
    m_hardwareError = true;

    // 可以在这里添加错误日志记录
}
