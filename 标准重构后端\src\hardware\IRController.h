#pragma once

#include "../core/DataStructures.h"
#include "../core/JSONConverter.h"
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include <IRrecv.h>
#include <IRutils.h>
#include <mutex>
#include <queue>
#include <functional>

/**
 * ESP32-S3 红外控制系统 - 红外控制器
 * 
 * 功能：
 * 1. 红外信号发送和接收
 * 2. 多协议支持和自动识别
 * 3. 信号学习和存储
 * 4. 硬件状态监控
 * 5. 异步操作和队列管理
 * 
 * 设计原则：
 * - 硬件抽象：封装IRremoteESP8266库
 * - 线程安全：互斥锁保护硬件访问
 * - 异步操作：非阻塞的发送和接收
 * - 错误处理：完整的硬件错误检测
 * - 状态管理：实时硬件状态监控
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 */

class IRController {
public:
    // 操作状态枚举
    enum class OperationState {
        IDLE = 0,           // 空闲状态
        SENDING = 1,        // 正在发送
        RECEIVING = 2,      // 正在接收
        LEARNING = 3,       // 正在学习
        ERROR = 4           // 错误状态
    };
    
    // 学习状态枚举
    enum class LearningState {
        STOPPED = 0,        // 已停止
        WAITING = 1,        // 等待信号
        RECEIVED = 2,       // 已接收
        TIMEOUT = 3,        // 超时
        ERROR = 4           // 错误
    };
    
    // 硬件配置结构
    struct HardwareConfig {
        uint8_t sendPin;            // 发送引脚
        uint8_t recvPin;            // 接收引脚
        uint16_t bufferSize;        // 接收缓冲区大小
        uint32_t timeout;           // 接收超时时间（毫秒）
        bool enableReceiver;        // 是否启用接收器
        bool enableSender;          // 是否启用发送器
        uint16_t frequency;         // 默认载波频率
        uint8_t dutyCycle;          // 占空比百分比
        
        HardwareConfig() : sendPin(4), recvPin(14), bufferSize(1024), timeout(15000),
                          enableReceiver(true), enableSender(true), frequency(38000), dutyCycle(33) {}
    };
    
    // 发送队列项目
    struct SendQueueItem {
        SignalData signal;
        std::function<void(bool)> callback;
        uint32_t priority;
        Timestamp timestamp;
        
        SendQueueItem(const SignalData& sig, std::function<void(bool)> cb, uint32_t prio = 0)
            : signal(sig), callback(cb), priority(prio), timestamp(millis()) {}
    };
    
    // 学习结果结构
    struct LearningResult {
        bool success;
        SignalData signal;
        String errorMessage;
        LearningState state;
        uint32_t duration;          // 学习耗时（毫秒）
        
        LearningResult() : success(false), state(LearningState::STOPPED), duration(0) {}
    };

private:
    // 硬件对象
    IRsend* m_irSend;
    IRrecv* m_irRecv;
    decode_results m_decodeResults;
    
    // 配置和状态
    HardwareConfig m_config;
    OperationState m_operationState;
    LearningState m_learningState;
    bool m_initialized;
    bool m_hardwareError;
    
    // 发送队列
    std::queue<SendQueueItem> m_sendQueue;
    bool m_queueProcessing;
    
    // 学习相关
    bool m_learningActive;
    Timestamp m_learningStartTime;
    uint32_t m_learningTimeout;
    SignalData m_learnedSignal;
    
    // 统计信息
    uint32_t m_totalSent;
    uint32_t m_totalReceived;
    uint32_t m_sendErrors;
    uint32_t m_receiveErrors;
    uint32_t m_learningAttempts;
    uint32_t m_learningSuccesses;
    
    // 线程安全
    mutable std::mutex m_mutex;
    
    // 回调函数
    std::function<void(const SignalData&)> m_signalReceivedCallback;
    std::function<void(const LearningResult&)> m_learningCompleteCallback;
    std::function<void(OperationState, OperationState)> m_stateChangeCallback;

public:
    /**
     * 构造函数
     * @param config 硬件配置
     */
    IRController(const HardwareConfig& config = HardwareConfig());
    
    /**
     * 析构函数
     */
    ~IRController();
    
    // ==================== 生命周期管理 ====================
    
    /**
     * 初始化红外控制器
     * @return 是否成功
     */
    bool initialize();
    
    /**
     * 清理资源
     */
    void cleanup();
    
    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return m_initialized; }
    
    /**
     * 检查硬件是否正常
     * @return 是否正常
     */
    bool isHardwareOK() const { return m_initialized && !m_hardwareError; }
    
    // ==================== 信号发送 ====================
    
    /**
     * 发送红外信号
     * @param signal 信号数据
     * @param callback 完成回调（可选）
     * @return 是否成功加入队列
     */
    bool sendSignal(const SignalData& signal, std::function<void(bool)> callback = nullptr);
    
    /**
     * 批量发送信号
     * @param signals 信号列表
     * @param callback 完成回调（可选）
     * @return 是否成功加入队列
     */
    bool sendSignalBatch(const std::vector<SignalData>& signals, std::function<void(bool)> callback = nullptr);
    
    /**
     * 优先发送信号（插队）
     * @param signal 信号数据
     * @param callback 完成回调（可选）
     * @return 是否成功加入队列
     */
    bool sendSignalPriority(const SignalData& signal, std::function<void(bool)> callback = nullptr);
    
    /**
     * 停止当前发送并清空队列
     * @return 是否成功
     */
    bool stopSending();
    
    /**
     * 获取发送队列大小
     * @return 队列大小
     */
    size_t getSendQueueSize() const;
    
    // ==================== 信号接收 ====================
    
    /**
     * 启用信号接收
     * @return 是否成功
     */
    bool enableReceiver();
    
    /**
     * 禁用信号接收
     * @return 是否成功
     */
    bool disableReceiver();
    
    /**
     * 检查是否有新信号
     * @return 是否有新信号
     */
    bool hasNewSignal();
    
    /**
     * 获取接收到的信号
     * @return 信号数据结果
     */
    Result<SignalData> getReceivedSignal();
    
    /**
     * 设置信号接收回调
     * @param callback 回调函数
     */
    void setSignalReceivedCallback(std::function<void(const SignalData&)> callback);
    
    // ==================== 信号学习 ====================
    
    /**
     * 开始学习信号
     * @param timeout 超时时间（毫秒）
     * @param callback 完成回调（可选）
     * @return 是否成功开始
     */
    bool startLearning(uint32_t timeout = 30000, std::function<void(const LearningResult&)> callback = nullptr);
    
    /**
     * 停止学习信号
     * @return 是否成功停止
     */
    bool stopLearning();
    
    /**
     * 获取学习状态
     * @return 学习状态
     */
    LearningState getLearningState() const;
    
    /**
     * 获取学习进度（百分比）
     * @return 进度百分比（0-100）
     */
    uint8_t getLearningProgress() const;
    
    /**
     * 保存学习到的信号
     * @param name 信号名称
     * @param description 信号描述
     * @param deviceType 设备类型
     * @return 信号数据结果
     */
    Result<SignalData> saveLearningResult(const String& name, const String& description = "", DeviceType deviceType = DeviceType::UNKNOWN);
    
    /**
     * 设置学习完成回调
     * @param callback 回调函数
     */
    void setLearningCompleteCallback(std::function<void(const LearningResult&)> callback);
    
    // ==================== 状态和监控 ====================
    
    /**
     * 获取当前操作状态
     * @return 操作状态
     */
    OperationState getOperationState() const;
    
    /**
     * 获取硬件状态信息
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getHardwareStatus(JsonDocument& doc) const;
    
    /**
     * 获取统计信息
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getStatistics(JsonDocument& doc) const;
    
    /**
     * 设置状态变化回调
     * @param callback 回调函数
     */
    void setStateChangeCallback(std::function<void(OperationState, OperationState)> callback);
    
    /**
     * 重置统计信息
     */
    void resetStatistics();
    
    // ==================== 配置管理 ====================
    
    /**
     * 更新硬件配置
     * @param config 新配置
     * @return 是否成功
     */
    bool updateConfig(const HardwareConfig& config);
    
    /**
     * 获取当前配置
     * @return 硬件配置
     */
    const HardwareConfig& getConfig() const { return m_config; }
    
    /**
     * 测试硬件连接
     * @return 测试结果
     */
    bool testHardware();
    
    // ==================== 循环处理 ====================
    
    /**
     * 主循环处理函数（需要在主循环中调用）
     */
    void loop();

private:
    // ==================== 内部实现方法 ====================
    
    /**
     * 初始化发送器
     * @return 是否成功
     */
    bool initializeSender();
    
    /**
     * 初始化接收器
     * @return 是否成功
     */
    bool initializeReceiver();
    
    /**
     * 处理发送队列
     */
    void processSendQueue();
    
    /**
     * 实际发送信号
     * @param signal 信号数据
     * @return 是否成功
     */
    bool doSendSignal(const SignalData& signal);
    
    /**
     * 处理接收到的信号
     */
    void processReceivedSignal();
    
    /**
     * 处理学习过程
     */
    void processLearning();
    
    /**
     * 转换协议枚举到库类型
     * @param protocol 协议枚举
     * @return 库协议类型
     */
    decode_type_t convertProtocolToLibType(IRProtocol protocol);
    
    /**
     * 转换库类型到协议枚举
     * @param libType 库协议类型
     * @return 协议枚举
     */
    IRProtocol convertLibTypeToProtocol(decode_type_t libType);
    
    /**
     * 设置操作状态
     * @param newState 新状态
     */
    void setOperationState(OperationState newState);
    
    /**
     * 设置学习状态
     * @param newState 新状态
     */
    void setLearningState(LearningState newState);
    
    /**
     * 验证信号数据
     * @param signal 信号数据
     * @return 是否有效
     */
    bool validateSignalData(const SignalData& signal);
    
    /**
     * 记录错误
     * @param operation 操作类型
     * @param error 错误信息
     */
    void recordError(const String& operation, const String& error);
};
