#include "WSManager.h"
#include <Arduino.h>

WSManager::WSManager(const WSConfig& config)
    : m_webSocket(nullptr), m_nextClientId(1), m_config(config),
      m_initialized(false), m_totalConnections(0), m_totalDisconnections(0),
      m_messagesSent(0), m_messagesReceived(0), m_authenticationFailures(0) {
    
    // 预分配消息队列
    m_messageQueue.reserve(m_config.messageQueueSize);
}

WSManager::~WSManager() {
    cleanup();
}

bool WSManager::initialize(AsyncWebServer* server, const String& path) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized || !server) {
        return false;
    }
    
    try {
        // 创建WebSocket服务器
        m_webSocket = new AsyncWebSocket(path);
        
        // 设置事件处理器
        m_webSocket->onEvent([this](AsyncWebSocket* server, AsyncWebSocketClient* client,
                                   AwsEventType type, void* arg, uint8_t* data, size_t len) {
            this->onWebSocketEvent(server, client, type, arg, data, len);
        });
        
        // 添加到Web服务器
        server->addHandler(m_webSocket);
        
        m_initialized = true;
        
        Serial.printf("✅ WSManager: Initialized on path %s\n", path.c_str());
        Serial.printf("   Max clients: %d, Ping interval: %dms\n", 
                     m_config.maxClients, m_config.pingInterval);
        
        return true;
    } catch (...) {
        Serial.printf("❌ WSManager: Failed to initialize on path %s\n", path.c_str());
        if (m_webSocket) {
            delete m_webSocket;
            m_webSocket = nullptr;
        }
        return false;
    }
}

void WSManager::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        // 断开所有客户端
        disconnectAllClients("Server shutdown");
        
        // 清理WebSocket
        if (m_webSocket) {
            m_webSocket->closeAll();
            delete m_webSocket;
            m_webSocket = nullptr;
        }
        
        // 清理客户端列表
        m_clients.clear();
        
        // 清理消息队列
        m_messageQueue.clear();
        
        m_initialized = false;
        
        Serial.println("✅ WSManager: Cleanup completed");
    }
}

size_t WSManager::getClientCount() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_clients.size();
}

std::vector<WSManager::ClientInfo> WSManager::getAllClients() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    std::vector<ClientInfo> result;
    result.reserve(m_clients.size());
    
    for (const auto& pair : m_clients) {
        result.push_back(pair.second);
    }
    
    return result;
}

Result<WSManager::ClientInfo> WSManager::getClientInfo(uint32_t clientId) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_clients.find(clientId);
    if (it != m_clients.end()) {
        return Result<ClientInfo>::Success(it->second);
    }
    
    return Result<ClientInfo>::Error(ErrorCode::RESOURCE_NOT_FOUND, "Client not found");
}

bool WSManager::disconnectClient(uint32_t clientId, const String& reason) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_clients.find(clientId);
    if (it == m_clients.end()) {
        return false;
    }
    
    AsyncWebSocketClient* client = it->second.client;
    if (client && client->status() == WS_CONNECTED) {
        if (!reason.isEmpty()) {
            // 发送断开原因
            DynamicJsonDocument doc(256);
            doc["type"] = "disconnect";
            doc["reason"] = reason;
            String message;
            serializeJson(doc, message);
            client->text(message);
        }
        
        client->close();
    }
    
    return true;
}

void WSManager::disconnectAllClients(const String& reason) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    for (auto& pair : m_clients) {
        AsyncWebSocketClient* client = pair.second.client;
        if (client && client->status() == WS_CONNECTED) {
            if (!reason.isEmpty()) {
                // 发送断开原因
                DynamicJsonDocument doc(256);
                doc["type"] = "disconnect";
                doc["reason"] = reason;
                String message;
                serializeJson(doc, message);
                client->text(message);
            }
            
            client->close();
        }
    }
}

bool WSManager::sendMessage(uint32_t clientId, MessageType type, const String& data, uint8_t priority) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return false;
    }
    
    auto it = m_clients.find(clientId);
    if (it == m_clients.end()) {
        return false;
    }
    
    AsyncWebSocketClient* client = it->second.client;
    if (!client || client->status() != WS_CONNECTED) {
        return false;
    }
    
    Message message(type, data, clientId, priority);
    return doSendMessage(client, message);
}

bool WSManager::sendJSONMessage(uint32_t clientId, MessageType type, const JsonObject& json, uint8_t priority) {
    String jsonString;
    serializeJson(json, jsonString);
    return sendMessage(clientId, type, jsonString, priority);
}

size_t WSManager::broadcastMessage(MessageType type, const String& data, uint8_t priority) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return 0;
    }
    
    size_t successCount = 0;
    Message message(type, data, 0, priority);
    
    for (auto& pair : m_clients) {
        AsyncWebSocketClient* client = pair.second.client;
        if (client && client->status() == WS_CONNECTED) {
            if (doSendMessage(client, message)) {
                successCount++;
            }
        }
    }
    
    return successCount;
}

size_t WSManager::broadcastJSONMessage(MessageType type, const JsonObject& json, uint8_t priority) {
    String jsonString;
    serializeJson(json, jsonString);
    return broadcastMessage(type, jsonString, priority);
}

size_t WSManager::sendToSubscribers(MessageType type, const String& data, uint8_t priority) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return 0;
    }
    
    size_t successCount = 0;
    Message message(type, data, 0, priority);
    
    for (auto& pair : m_clients) {
        ClientInfo& clientInfo = pair.second;
        
        // 检查是否订阅了该消息类型
        bool subscribed = std::find(clientInfo.subscriptions.begin(), 
                                   clientInfo.subscriptions.end(), type) != clientInfo.subscriptions.end();
        
        if (subscribed && clientInfo.client && clientInfo.client->status() == WS_CONNECTED) {
            if (doSendMessage(clientInfo.client, message)) {
                successCount++;
            }
        }
    }
    
    return successCount;
}

bool WSManager::subscribeClient(uint32_t clientId, MessageType type) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_clients.find(clientId);
    if (it == m_clients.end()) {
        return false;
    }
    
    auto& subscriptions = it->second.subscriptions;
    if (std::find(subscriptions.begin(), subscriptions.end(), type) == subscriptions.end()) {
        subscriptions.push_back(type);
    }
    
    return true;
}

bool WSManager::unsubscribeClient(uint32_t clientId, MessageType type) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_clients.find(clientId);
    if (it == m_clients.end()) {
        return false;
    }
    
    auto& subscriptions = it->second.subscriptions;
    subscriptions.erase(std::remove(subscriptions.begin(), subscriptions.end(), type), subscriptions.end());
    
    return true;
}

std::vector<WSManager::MessageType> WSManager::getClientSubscriptions(uint32_t clientId) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_clients.find(clientId);
    if (it != m_clients.end()) {
        return it->second.subscriptions;
    }
    
    return {};
}

std::vector<uint32_t> WSManager::getSubscribers(MessageType type) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    std::vector<uint32_t> subscribers;
    
    for (const auto& pair : m_clients) {
        const auto& subscriptions = pair.second.subscriptions;
        if (std::find(subscriptions.begin(), subscriptions.end(), type) != subscriptions.end()) {
            subscribers.push_back(pair.first);
        }
    }
    
    return subscribers;
}

void WSManager::pushSystemStatus(const JsonObject& status) {
    sendToSubscribers(MessageType::SYSTEM_STATUS, "", 200);
}

void WSManager::pushSignalStatus(SignalID signalId, const String& status) {
    DynamicJsonDocument doc(512);
    doc["signalId"] = String(signalId);
    doc["status"] = status;
    doc["timestamp"] = millis();
    
    String jsonString;
    serializeJson(doc, jsonString);
    
    sendToSubscribers(MessageType::SIGNAL_STATUS, jsonString, 150);
}

void WSManager::pushTaskStatus(TaskID taskId, TaskStatus status) {
    DynamicJsonDocument doc(512);
    doc["taskId"] = String(taskId);
    doc["status"] = JSONConverter::taskStatusToString(status);
    doc["timestamp"] = millis();
    
    String jsonString;
    serializeJson(doc, jsonString);
    
    sendToSubscribers(MessageType::TASK_STATUS, jsonString, 150);
}

void WSManager::pushTimerStatus(TimerID timerId, bool enabled) {
    DynamicJsonDocument doc(512);
    doc["timerId"] = String(timerId);
    doc["enabled"] = enabled;
    doc["timestamp"] = millis();
    
    String jsonString;
    serializeJson(doc, jsonString);
    
    sendToSubscribers(MessageType::TIMER_STATUS, jsonString, 150);
}

void WSManager::pushLearningStatus(const String& state, uint8_t progress) {
    DynamicJsonDocument doc(512);
    doc["state"] = state;
    doc["progress"] = progress;
    doc["timestamp"] = millis();
    
    String jsonString;
    serializeJson(doc, jsonString);
    
    sendToSubscribers(MessageType::LEARNING_STATUS, jsonString, 200);
}

void WSManager::pushHardwareStatus(const JsonObject& status) {
    String jsonString;
    serializeJson(status, jsonString);
    
    sendToSubscribers(MessageType::HARDWARE_STATUS, jsonString, 180);
}

void WSManager::pushErrorNotification(const String& error, ErrorLevel level) {
    DynamicJsonDocument doc(1024);
    doc["error"] = error;
    doc["level"] = static_cast<int>(level);
    doc["timestamp"] = millis();
    
    String jsonString;
    serializeJson(doc, jsonString);
    
    sendToSubscribers(MessageType::ERROR_NOTIFICATION, jsonString, 255);
}

void WSManager::setAuthenticationCallback(std::function<bool(const String&, const String&)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_authenticationCallback = callback;
}

bool WSManager::authenticateClient(uint32_t clientId, const String& token) {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_clients.find(clientId);
    if (it == m_clients.end()) {
        return false;
    }

    bool authenticated = false;

    if (m_config.enableAuthentication) {
        if (m_authenticationCallback) {
            authenticated = m_authenticationCallback(it->second.remoteIP, token);
        } else {
            authenticated = (token == m_config.authToken);
        }
    } else {
        authenticated = true; // 未启用认证时默认通过
    }

    it->second.authenticated = authenticated;

    if (!authenticated) {
        m_authenticationFailures++;
    }

    return authenticated;
}

bool WSManager::isClientAuthenticated(uint32_t clientId) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_clients.find(clientId);
    if (it != m_clients.end()) {
        return it->second.authenticated;
    }

    return false;
}

void WSManager::setMessageReceivedCallback(std::function<void(uint32_t, const String&)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_messageReceivedCallback = callback;
}

void WSManager::setClientConnectedCallback(std::function<void(uint32_t)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_clientConnectedCallback = callback;
}

void WSManager::setClientDisconnectedCallback(std::function<void(uint32_t)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_clientDisconnectedCallback = callback;
}

JsonObject WSManager::getStatistics(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject stats = doc.createNestedObject("websocket_statistics");

    stats["initialized"] = m_initialized;
    stats["current_clients"] = m_clients.size();
    stats["total_connections"] = m_totalConnections;
    stats["total_disconnections"] = m_totalDisconnections;
    stats["messages_sent"] = m_messagesSent;
    stats["messages_received"] = m_messagesReceived;
    stats["authentication_failures"] = m_authenticationFailures;
    stats["message_queue_size"] = m_messageQueue.size();

    // 配置信息
    JsonObject config = stats.createNestedObject("config");
    config["max_clients"] = m_config.maxClients;
    config["ping_interval"] = m_config.pingInterval;
    config["pong_timeout"] = m_config.pongTimeout;
    config["enable_authentication"] = m_config.enableAuthentication;
    config["enable_compression"] = m_config.enableCompression;

    return stats;
}

void WSManager::resetStatistics() {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_totalConnections = 0;
    m_totalDisconnections = 0;
    m_messagesSent = 0;
    m_messagesReceived = 0;
    m_authenticationFailures = 0;

    Serial.println("✅ WSManager: Statistics reset");
}

JsonObject WSManager::getConnectionStatus(JsonDocument& doc) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject status = doc.createNestedObject("connection_status");

    status["client_count"] = m_clients.size();
    status["max_clients"] = m_config.maxClients;

    JsonArray clients = status.createNestedArray("clients");
    for (const auto& pair : m_clients) {
        const ClientInfo& info = pair.second;
        JsonObject clientObj = clients.createNestedObject();

        clientObj["id"] = info.id;
        clientObj["remote_ip"] = info.remoteIP;
        clientObj["user_agent"] = info.userAgent;
        clientObj["connected_time"] = info.connectedTime;
        clientObj["last_ping"] = info.lastPingTime;
        clientObj["authenticated"] = info.authenticated;
        clientObj["subscriptions_count"] = info.subscriptions.size();

        if (info.client) {
            clientObj["status"] = (info.client->status() == WS_CONNECTED) ? "connected" : "disconnected";
        }
    }

    return status;
}

bool WSManager::updateConfig(const WSConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 验证配置
    if (config.maxClients == 0 || config.pingInterval == 0 || config.pongTimeout == 0) {
        Serial.println("❌ WSManager: Invalid configuration parameters");
        return false;
    }

    m_config = config;

    // 重新分配消息队列
    m_messageQueue.clear();
    m_messageQueue.reserve(m_config.messageQueueSize);

    Serial.println("✅ WSManager: Configuration updated");
    return true;
}

void WSManager::loop() {
    if (!m_initialized) {
        return;
    }

    // 检查客户端连接状态
    checkClientConnections();

    // 处理消息队列
    processMessageQueue();

    // 清理WebSocket
    if (m_webSocket) {
        m_webSocket->cleanupClients();
    }
}

// ==================== 内部实现方法 ====================

void WSManager::onWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client,
                                AwsEventType type, void* arg, uint8_t* data, size_t len) {
    switch (type) {
        case WS_EVT_CONNECT:
            handleClientConnect(client);
            break;

        case WS_EVT_DISCONNECT:
            handleClientDisconnect(client);
            break;

        case WS_EVT_DATA:
            handleMessage(client, data, len);
            break;

        case WS_EVT_PONG:
            {
                uint32_t clientId = findClientId(client);
                if (clientId != 0) {
                    handleHeartbeat(clientId);
                }
            }
            break;

        case WS_EVT_ERROR:
            Serial.printf("⚠️ WSManager: WebSocket error for client %s\n",
                         client->remoteIP().toString().c_str());
            break;
    }
}

void WSManager::handleClientConnect(AsyncWebSocketClient* client) {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查客户端数量限制
    if (m_clients.size() >= m_config.maxClients) {
        Serial.printf("⚠️ WSManager: Max clients reached, rejecting %s\n",
                     client->remoteIP().toString().c_str());
        client->close();
        return;
    }

    // 创建客户端信息
    ClientInfo info;
    info.id = m_nextClientId++;
    info.client = client;
    info.remoteIP = client->remoteIP().toString();
    info.connectedTime = millis();
    info.lastPingTime = info.connectedTime;
    info.authenticated = !m_config.enableAuthentication; // 如果未启用认证则默认通过

    // 获取用户代理（如果有）
    if (client->hasHeader("User-Agent")) {
        info.userAgent = client->header("User-Agent");
    }

    m_clients[info.id] = info;
    m_totalConnections++;

    Serial.printf("✅ WSManager: Client connected - ID:%d, IP:%s\n",
                 info.id, info.remoteIP.c_str());

    // 发送欢迎消息
    DynamicJsonDocument doc(512);
    doc["type"] = "welcome";
    doc["clientId"] = info.id;
    doc["serverTime"] = millis();
    doc["authRequired"] = m_config.enableAuthentication;

    String welcome;
    serializeJson(doc, welcome);
    client->text(welcome);

    // 调用连接回调
    if (m_clientConnectedCallback) {
        m_clientConnectedCallback(info.id);
    }
}

void WSManager::handleClientDisconnect(AsyncWebSocketClient* client) {
    std::lock_guard<std::mutex> lock(m_mutex);

    uint32_t clientId = findClientId(client);
    if (clientId != 0) {
        auto it = m_clients.find(clientId);
        if (it != m_clients.end()) {
            Serial.printf("✅ WSManager: Client disconnected - ID:%d, IP:%s\n",
                         clientId, it->second.remoteIP.c_str());

            // 调用断开回调
            if (m_clientDisconnectedCallback) {
                m_clientDisconnectedCallback(clientId);
            }

            m_clients.erase(it);
            m_totalDisconnections++;
        }
    }
}

void WSManager::handleMessage(AsyncWebSocketClient* client, uint8_t* data, size_t len) {
    std::lock_guard<std::mutex> lock(m_mutex);

    uint32_t clientId = findClientId(client);
    if (clientId == 0) {
        return;
    }

    // 检查认证状态
    if (m_config.enableAuthentication && !isClientAuthenticated(clientId)) {
        // 尝试解析认证消息
        String message = String((char*)data, len);
        DynamicJsonDocument doc(512);

        if (deserializeJson(doc, message) == DeserializationError::Ok) {
            if (doc["type"] == "auth" && doc.containsKey("token")) {
                String token = doc["token"];
                if (authenticateClient(clientId, token)) {
                    // 认证成功
                    DynamicJsonDocument response(256);
                    response["type"] = "auth_result";
                    response["success"] = true;

                    String responseStr;
                    serializeJson(response, responseStr);
                    client->text(responseStr);
                } else {
                    // 认证失败
                    DynamicJsonDocument response(256);
                    response["type"] = "auth_result";
                    response["success"] = false;
                    response["error"] = "Invalid token";

                    String responseStr;
                    serializeJson(response, responseStr);
                    client->text(responseStr);

                    // 延迟断开连接
                    client->close();
                }
                return;
            }
        }

        // 未认证的客户端只能发送认证消息
        return;
    }

    String message = String((char*)data, len);
    m_messagesReceived++;

    // 解析消息
    DynamicJsonDocument doc(1024);
    if (deserializeJson(doc, message) == DeserializationError::Ok) {
        String type = doc["type"];

        if (type == "ping") {
            handleHeartbeat(clientId);
        } else if (type == "subscribe") {
            if (doc.containsKey("messageType")) {
                MessageType msgType = static_cast<MessageType>(doc["messageType"].as<int>());
                subscribeClient(clientId, msgType);
            }
        } else if (type == "unsubscribe") {
            if (doc.containsKey("messageType")) {
                MessageType msgType = static_cast<MessageType>(doc["messageType"].as<int>());
                unsubscribeClient(clientId, msgType);
            }
        }
    }

    // 调用消息接收回调
    if (m_messageReceivedCallback) {
        m_messageReceivedCallback(clientId, message);
    }
}

void WSManager::handleHeartbeat(uint32_t clientId) {
    auto it = m_clients.find(clientId);
    if (it != m_clients.end()) {
        it->second.lastPingTime = millis();

        // 发送pong响应
        if (it->second.client && it->second.client->status() == WS_CONNECTED) {
            DynamicJsonDocument doc(128);
            doc["type"] = "pong";
            doc["timestamp"] = millis();

            String pong;
            serializeJson(doc, pong);
            it->second.client->text(pong);
        }
    }
}

void WSManager::checkClientConnections() {
    std::lock_guard<std::mutex> lock(m_mutex);

    Timestamp currentTime = millis();
    std::vector<uint32_t> disconnectedClients;

    for (auto& pair : m_clients) {
        ClientInfo& info = pair.second;

        // 检查心跳超时
        if (currentTime - info.lastPingTime > m_config.pongTimeout) {
            Serial.printf("⚠️ WSManager: Client %d heartbeat timeout\n", info.id);
            disconnectedClients.push_back(info.id);
            continue;
        }

        // 检查连接状态
        if (!info.client || info.client->status() != WS_CONNECTED) {
            Serial.printf("⚠️ WSManager: Client %d connection lost\n", info.id);
            disconnectedClients.push_back(info.id);
            continue;
        }

        // 发送心跳ping
        if (currentTime - info.lastPingTime > m_config.pingInterval) {
            DynamicJsonDocument doc(128);
            doc["type"] = "ping";
            doc["timestamp"] = currentTime;

            String ping;
            serializeJson(doc, ping);
            info.client->ping(ping.c_str());
        }
    }

    // 移除断开的客户端
    for (uint32_t clientId : disconnectedClients) {
        auto it = m_clients.find(clientId);
        if (it != m_clients.end()) {
            if (m_clientDisconnectedCallback) {
                m_clientDisconnectedCallback(clientId);
            }
            m_clients.erase(it);
            m_totalDisconnections++;
        }
    }
}

void WSManager::processMessageQueue() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_messageQueue.empty()) {
        return;
    }

    // 按优先级排序（高优先级在前）
    std::sort(m_messageQueue.begin(), m_messageQueue.end(),
             [](const Message& a, const Message& b) {
                 return a.priority > b.priority;
             });

    // 处理队列中的消息
    auto it = m_messageQueue.begin();
    while (it != m_messageQueue.end()) {
        const Message& message = *it;
        bool sent = false;

        if (message.targetClientId == 0) {
            // 广播消息
            for (auto& pair : m_clients) {
                AsyncWebSocketClient* client = pair.second.client;
                if (client && client->status() == WS_CONNECTED) {
                    doSendMessage(client, message);
                    sent = true;
                }
            }
        } else {
            // 单播消息
            auto clientIt = m_clients.find(message.targetClientId);
            if (clientIt != m_clients.end()) {
                AsyncWebSocketClient* client = clientIt->second.client;
                if (client && client->status() == WS_CONNECTED) {
                    sent = doSendMessage(client, message);
                }
            }
        }

        if (sent) {
            m_messagesSent++;
        }

        it = m_messageQueue.erase(it);
    }
}

bool WSManager::doSendMessage(AsyncWebSocketClient* client, const Message& message) {
    if (!client || client->status() != WS_CONNECTED) {
        return false;
    }

    try {
        DynamicJsonDocument doc(1024 + message.data.length());
        String messageJson = createMessageJSON(message.type, message.data, doc);

        if (m_config.enableCompression && messageJson.length() > 256) {
            // 对大消息启用压缩
            client->text(messageJson);
        } else {
            client->text(messageJson);
        }

        return true;
    } catch (...) {
        Serial.printf("❌ WSManager: Failed to send message to client %s\n",
                     client->remoteIP().toString().c_str());
        return false;
    }
}

String WSManager::createMessageJSON(MessageType type, const String& data, JsonDocument& doc) {
    doc["type"] = messageTypeToString(type);
    doc["timestamp"] = millis();

    if (!data.isEmpty()) {
        // 尝试解析数据为JSON
        DynamicJsonDocument dataDoc(data.length() + 256);
        if (deserializeJson(dataDoc, data) == DeserializationError::Ok) {
            doc["data"] = dataDoc.as<JsonVariant>();
        } else {
            doc["data"] = data;
        }
    }

    String result;
    serializeJson(doc, result);
    return result;
}

uint32_t WSManager::findClientId(AsyncWebSocketClient* client) const {
    for (const auto& pair : m_clients) {
        if (pair.second.client == client) {
            return pair.first;
        }
    }
    return 0;
}

String WSManager::messageTypeToString(MessageType type) const {
    switch (type) {
        case MessageType::SYSTEM_STATUS: return "system_status";
        case MessageType::SIGNAL_STATUS: return "signal_status";
        case MessageType::TASK_STATUS: return "task_status";
        case MessageType::TIMER_STATUS: return "timer_status";
        case MessageType::LEARNING_STATUS: return "learning_status";
        case MessageType::HARDWARE_STATUS: return "hardware_status";
        case MessageType::ERROR_NOTIFICATION: return "error_notification";
        case MessageType::HEARTBEAT: return "heartbeat";
        case MessageType::CUSTOM: return "custom";
        default: return "unknown";
    }
}
