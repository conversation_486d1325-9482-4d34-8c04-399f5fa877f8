#pragma once

#include "../core/DataStructures.h"
#include "../core/JSONConverter.h"
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>
#include <vector>
#include <map>
#include <functional>
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - WebSocket管理器
 * 
 * 功能：
 * 1. WebSocket连接管理
 * 2. 实时消息推送
 * 3. 客户端状态同步
 * 4. 事件广播和订阅
 * 5. 连接认证和安全
 * 
 * 设计原则：
 * - 连接管理：自动连接检测和清理
 * - 消息路由：基于类型的消息分发
 * - 状态同步：实时推送系统状态
 * - 安全控制：连接认证和权限管理
 * - 性能优化：消息队列和批量发送
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 */

class WSManager {
public:
    // 消息类型枚举
    enum class MessageType {
        SYSTEM_STATUS = 0,      // 系统状态
        SIGNAL_STATUS = 1,      // 信号状态
        TASK_STATUS = 2,        // 任务状态
        TIMER_STATUS = 3,       // 定时器状态
        LEARNING_STATUS = 4,    // 学习状态
        HARDWARE_STATUS = 5,    // 硬件状态
        ERROR_NOTIFICATION = 6, // 错误通知
        HEARTBEAT = 7,          // 心跳
        CUSTOM = 255            // 自定义消息
    };
    
    // 客户端信息结构
    struct ClientInfo {
        uint32_t id;                    // 客户端ID
        AsyncWebSocketClient* client;   // WebSocket客户端对象
        String userAgent;               // 用户代理
        String remoteIP;                // 远程IP地址
        Timestamp connectedTime;        // 连接时间
        Timestamp lastPingTime;         // 最后心跳时间
        bool authenticated;             // 是否已认证
        std::vector<MessageType> subscriptions; // 订阅的消息类型
        
        ClientInfo() : id(0), client(nullptr), connectedTime(0), lastPingTime(0), authenticated(false) {}
    };
    
    // 消息结构
    struct Message {
        MessageType type;
        String data;
        uint32_t targetClientId;    // 0表示广播
        Timestamp timestamp;
        uint8_t priority;           // 优先级 0-255
        
        Message(MessageType t, const String& d, uint32_t target = 0, uint8_t prio = 128)
            : type(t), data(d), targetClientId(target), timestamp(millis()), priority(prio) {}
    };
    
    // 配置结构
    struct WSConfig {
        uint16_t maxClients;            // 最大客户端数量
        uint32_t pingInterval;          // 心跳间隔（毫秒）
        uint32_t pongTimeout;           // 心跳超时（毫秒）
        uint32_t messageQueueSize;      // 消息队列大小
        bool enableAuthentication;      // 是否启用认证
        String authToken;               // 认证令牌
        bool enableCompression;         // 是否启用压缩
        
        WSConfig() : maxClients(10), pingInterval(30000), pongTimeout(60000),
                    messageQueueSize(100), enableAuthentication(false),
                    enableCompression(false) {}
    };

private:
    // WebSocket服务器
    AsyncWebSocket* m_webSocket;
    
    // 客户端管理
    std::map<uint32_t, ClientInfo> m_clients;
    uint32_t m_nextClientId;
    
    // 消息队列
    std::vector<Message> m_messageQueue;
    
    // 配置和状态
    WSConfig m_config;
    bool m_initialized;
    
    // 统计信息
    uint32_t m_totalConnections;
    uint32_t m_totalDisconnections;
    uint32_t m_messagesSent;
    uint32_t m_messagesReceived;
    uint32_t m_authenticationFailures;
    
    // 线程安全
    mutable std::mutex m_mutex;
    
    // 回调函数
    std::function<void(uint32_t, const String&)> m_messageReceivedCallback;
    std::function<void(uint32_t)> m_clientConnectedCallback;
    std::function<void(uint32_t)> m_clientDisconnectedCallback;
    std::function<bool(const String&, const String&)> m_authenticationCallback;

public:
    /**
     * 构造函数
     * @param config WebSocket配置
     */
    WSManager(const WSConfig& config = WSConfig());
    
    /**
     * 析构函数
     */
    ~WSManager();
    
    // ==================== 生命周期管理 ====================
    
    /**
     * 初始化WebSocket管理器
     * @param server Web服务器实例
     * @param path WebSocket路径
     * @return 是否成功
     */
    bool initialize(AsyncWebServer* server, const String& path = "/ws");
    
    /**
     * 清理资源
     */
    void cleanup();
    
    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return m_initialized; }
    
    // ==================== 连接管理 ====================
    
    /**
     * 获取连接的客户端数量
     * @return 客户端数量
     */
    size_t getClientCount() const;
    
    /**
     * 获取所有客户端信息
     * @return 客户端信息列表
     */
    std::vector<ClientInfo> getAllClients() const;
    
    /**
     * 根据ID获取客户端信息
     * @param clientId 客户端ID
     * @return 客户端信息结果
     */
    Result<ClientInfo> getClientInfo(uint32_t clientId) const;
    
    /**
     * 断开指定客户端
     * @param clientId 客户端ID
     * @param reason 断开原因
     * @return 是否成功
     */
    bool disconnectClient(uint32_t clientId, const String& reason = "");
    
    /**
     * 断开所有客户端
     * @param reason 断开原因
     */
    void disconnectAllClients(const String& reason = "Server shutdown");
    
    // ==================== 消息发送 ====================
    
    /**
     * 向指定客户端发送消息
     * @param clientId 客户端ID
     * @param type 消息类型
     * @param data 消息数据
     * @param priority 优先级
     * @return 是否成功
     */
    bool sendMessage(uint32_t clientId, MessageType type, const String& data, uint8_t priority = 128);
    
    /**
     * 向指定客户端发送JSON消息
     * @param clientId 客户端ID
     * @param type 消息类型
     * @param json JSON对象
     * @param priority 优先级
     * @return 是否成功
     */
    bool sendJSONMessage(uint32_t clientId, MessageType type, const JsonObject& json, uint8_t priority = 128);
    
    /**
     * 广播消息给所有客户端
     * @param type 消息类型
     * @param data 消息数据
     * @param priority 优先级
     * @return 发送成功的客户端数量
     */
    size_t broadcastMessage(MessageType type, const String& data, uint8_t priority = 128);
    
    /**
     * 广播JSON消息给所有客户端
     * @param type 消息类型
     * @param json JSON对象
     * @param priority 优先级
     * @return 发送成功的客户端数量
     */
    size_t broadcastJSONMessage(MessageType type, const JsonObject& json, uint8_t priority = 128);
    
    /**
     * 向订阅了指定类型的客户端发送消息
     * @param type 消息类型
     * @param data 消息数据
     * @param priority 优先级
     * @return 发送成功的客户端数量
     */
    size_t sendToSubscribers(MessageType type, const String& data, uint8_t priority = 128);
    
    // ==================== 订阅管理 ====================
    
    /**
     * 客户端订阅消息类型
     * @param clientId 客户端ID
     * @param type 消息类型
     * @return 是否成功
     */
    bool subscribeClient(uint32_t clientId, MessageType type);
    
    /**
     * 客户端取消订阅消息类型
     * @param clientId 客户端ID
     * @param type 消息类型
     * @return 是否成功
     */
    bool unsubscribeClient(uint32_t clientId, MessageType type);
    
    /**
     * 获取客户端的订阅列表
     * @param clientId 客户端ID
     * @return 订阅的消息类型列表
     */
    std::vector<MessageType> getClientSubscriptions(uint32_t clientId) const;
    
    /**
     * 获取订阅了指定类型的客户端列表
     * @param type 消息类型
     * @return 客户端ID列表
     */
    std::vector<uint32_t> getSubscribers(MessageType type) const;
    
    // ==================== 状态推送 ====================
    
    /**
     * 推送系统状态
     * @param status 系统状态JSON
     */
    void pushSystemStatus(const JsonObject& status);
    
    /**
     * 推送信号状态
     * @param signalId 信号ID
     * @param status 信号状态
     */
    void pushSignalStatus(SignalID signalId, const String& status);
    
    /**
     * 推送任务状态
     * @param taskId 任务ID
     * @param status 任务状态
     */
    void pushTaskStatus(TaskID taskId, TaskStatus status);
    
    /**
     * 推送定时器状态
     * @param timerId 定时器ID
     * @param enabled 是否启用
     */
    void pushTimerStatus(TimerID timerId, bool enabled);
    
    /**
     * 推送学习状态
     * @param state 学习状态
     * @param progress 进度百分比
     */
    void pushLearningStatus(const String& state, uint8_t progress);
    
    /**
     * 推送硬件状态
     * @param status 硬件状态JSON
     */
    void pushHardwareStatus(const JsonObject& status);
    
    /**
     * 推送错误通知
     * @param error 错误信息
     * @param level 错误级别
     */
    void pushErrorNotification(const String& error, ErrorLevel level);
    
    // ==================== 认证管理 ====================
    
    /**
     * 设置认证回调
     * @param callback 认证回调函数
     */
    void setAuthenticationCallback(std::function<bool(const String&, const String&)> callback);
    
    /**
     * 认证客户端
     * @param clientId 客户端ID
     * @param token 认证令牌
     * @return 是否成功
     */
    bool authenticateClient(uint32_t clientId, const String& token);
    
    /**
     * 检查客户端是否已认证
     * @param clientId 客户端ID
     * @return 是否已认证
     */
    bool isClientAuthenticated(uint32_t clientId) const;
    
    // ==================== 回调设置 ====================
    
    /**
     * 设置消息接收回调
     * @param callback 回调函数
     */
    void setMessageReceivedCallback(std::function<void(uint32_t, const String&)> callback);
    
    /**
     * 设置客户端连接回调
     * @param callback 回调函数
     */
    void setClientConnectedCallback(std::function<void(uint32_t)> callback);
    
    /**
     * 设置客户端断开回调
     * @param callback 回调函数
     */
    void setClientDisconnectedCallback(std::function<void(uint32_t)> callback);
    
    // ==================== 统计和监控 ====================
    
    /**
     * 获取统计信息
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getStatistics(JsonDocument& doc) const;
    
    /**
     * 重置统计信息
     */
    void resetStatistics();
    
    /**
     * 获取连接状态
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getConnectionStatus(JsonDocument& doc) const;
    
    // ==================== 配置管理 ====================
    
    /**
     * 更新配置
     * @param config 新配置
     * @return 是否成功
     */
    bool updateConfig(const WSConfig& config);
    
    /**
     * 获取当前配置
     * @return WebSocket配置
     */
    const WSConfig& getConfig() const { return m_config; }
    
    // ==================== 循环处理 ====================
    
    /**
     * 主循环处理函数（需要在主循环中调用）
     */
    void loop();

private:
    // ==================== 内部实现方法 ====================
    
    /**
     * WebSocket事件处理
     * @param server WebSocket服务器
     * @param client 客户端
     * @param type 事件类型
     * @param arg 参数
     * @param data 数据
     * @param len 数据长度
     */
    void onWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client,
                         AwsEventType type, void* arg, uint8_t* data, size_t len);
    
    /**
     * 处理客户端连接
     * @param client 客户端
     */
    void handleClientConnect(AsyncWebSocketClient* client);
    
    /**
     * 处理客户端断开
     * @param client 客户端
     */
    void handleClientDisconnect(AsyncWebSocketClient* client);
    
    /**
     * 处理接收到的消息
     * @param client 客户端
     * @param data 消息数据
     * @param len 数据长度
     */
    void handleMessage(AsyncWebSocketClient* client, uint8_t* data, size_t len);
    
    /**
     * 处理心跳消息
     * @param clientId 客户端ID
     */
    void handleHeartbeat(uint32_t clientId);
    
    /**
     * 检查客户端连接状态
     */
    void checkClientConnections();
    
    /**
     * 处理消息队列
     */
    void processMessageQueue();
    
    /**
     * 实际发送消息
     * @param client 客户端
     * @param message 消息
     * @return 是否成功
     */
    bool doSendMessage(AsyncWebSocketClient* client, const Message& message);
    
    /**
     * 创建消息JSON
     * @param type 消息类型
     * @param data 消息数据
     * @param doc JSON文档
     * @return JSON字符串
     */
    String createMessageJSON(MessageType type, const String& data, JsonDocument& doc);
    
    /**
     * 根据客户端对象查找客户端ID
     * @param client 客户端对象
     * @return 客户端ID
     */
    uint32_t findClientId(AsyncWebSocketClient* client) const;
    
    /**
     * 消息类型转字符串
     * @param type 消息类型
     * @return 字符串表示
     */
    String messageTypeToString(MessageType type) const;
};
