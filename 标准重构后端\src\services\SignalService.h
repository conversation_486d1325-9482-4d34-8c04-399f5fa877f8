#pragma once

#include "../core/DataStructures.h"
#include "../data/SignalRepository.h"
#include "../hardware/IRController.h"
#include "../network/WSManager.h"
#include <functional>
#include <mutex>

/**
 * ESP32-S3 红外控制系统 - 信号业务服务
 * 
 * 功能：
 * 1. 信号业务逻辑封装
 * 2. 信号发送和学习管理
 * 3. 批量操作和事务处理
 * 4. 状态同步和事件通知
 * 5. 业务规则验证和执行
 * 
 * 设计原则：
 * - 业务封装：封装复杂的业务逻辑
 * - 事务管理：确保数据一致性
 * - 事件驱动：实时状态同步
 * - 错误处理：完整的业务错误处理
 * - 性能优化：缓存和批量操作
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 */

class SignalService {
public:
    // 学习配置结构
    struct LearningConfig {
        uint32_t timeout;               // 学习超时时间（毫秒）
        bool autoSave;                  // 是否自动保存
        String defaultName;             // 默认信号名称
        DeviceType defaultDeviceType;   // 默认设备类型
        bool enableValidation;          // 是否启用验证
        
        LearningConfig() : timeout(30000), autoSave(true), defaultName("New Signal"),
                          defaultDeviceType(DeviceType::UNKNOWN), enableValidation(true) {}
    };
    
    // 发送配置结构
    struct SendConfig {
        uint32_t retryCount;            // 重试次数
        uint32_t retryDelay;            // 重试延迟（毫秒）
        bool enableFeedback;            // 是否启用反馈
        uint8_t priority;               // 发送优先级
        
        SendConfig() : retryCount(3), retryDelay(100), enableFeedback(true), priority(128) {}
    };
    
    // 批量操作结果
    struct BatchResult {
        size_t totalCount;              // 总数量
        size_t successCount;            // 成功数量
        size_t failureCount;            // 失败数量
        std::vector<String> errors;     // 错误列表
        uint32_t duration;              // 执行时间（毫秒）
        
        BatchResult() : totalCount(0), successCount(0), failureCount(0), duration(0) {}
    };

private:
    // 依赖组件
    SignalRepository* m_repository;
    IRController* m_irController;
    WSManager* m_wsManager;
    
    // 配置
    LearningConfig m_learningConfig;
    SendConfig m_sendConfig;
    
    // 状态
    bool m_initialized;
    bool m_learningActive;
    SignalData m_currentLearningSignal;
    
    // 统计信息
    uint32_t m_totalSignalsSent;
    uint32_t m_totalSignalsLearned;
    uint32_t m_totalBatchOperations;
    uint32_t m_sendFailures;
    uint32_t m_learningFailures;
    
    // 线程安全
    mutable std::mutex m_mutex;
    
    // 回调函数
    std::function<void(const SignalData&)> m_signalLearnedCallback;
    std::function<void(SignalID, bool)> m_signalSentCallback;
    std::function<void(const String&)> m_errorCallback;

public:
    /**
     * 构造函数
     * @param repository 信号仓库
     * @param irController 红外控制器
     * @param wsManager WebSocket管理器
     */
    SignalService(SignalRepository* repository, IRController* irController, WSManager* wsManager);
    
    /**
     * 析构函数
     */
    ~SignalService();
    
    // ==================== 生命周期管理 ====================
    
    /**
     * 初始化信号服务
     * @return 是否成功
     */
    bool initialize();
    
    /**
     * 清理资源
     */
    void cleanup();
    
    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return m_initialized; }
    
    // ==================== 信号CRUD操作 ====================
    
    /**
     * 创建新信号
     * @param signal 信号数据
     * @return 操作结果
     */
    Result<SignalData> createSignal(const SignalData& signal);
    
    /**
     * 获取信号
     * @param signalId 信号ID
     * @return 操作结果
     */
    Result<SignalData> getSignal(SignalID signalId);
    
    /**
     * 更新信号
     * @param signal 信号数据
     * @return 操作结果
     */
    Result<SignalData> updateSignal(const SignalData& signal);
    
    /**
     * 删除信号
     * @param signalId 信号ID
     * @return 是否成功
     */
    bool deleteSignal(SignalID signalId);
    
    /**
     * 获取所有信号
     * @return 信号列表
     */
    std::vector<SignalData> getAllSignals();
    
    // ==================== 信号发送 ====================
    
    /**
     * 发送信号
     * @param signalId 信号ID
     * @param config 发送配置（可选）
     * @return 是否成功
     */
    bool sendSignal(SignalID signalId, const SendConfig& config = SendConfig());
    
    /**
     * 发送信号（直接传入信号数据）
     * @param signal 信号数据
     * @param config 发送配置（可选）
     * @return 是否成功
     */
    bool sendSignalDirect(const SignalData& signal, const SendConfig& config = SendConfig());
    
    /**
     * 批量发送信号
     * @param signalIds 信号ID列表
     * @param config 发送配置（可选）
     * @return 批量操作结果
     */
    BatchResult sendSignalBatch(const std::vector<SignalID>& signalIds, const SendConfig& config = SendConfig());
    
    /**
     * 停止当前发送
     * @return 是否成功
     */
    bool stopSending();
    
    /**
     * 获取发送队列状态
     * @return 队列大小
     */
    size_t getSendQueueSize();
    
    // ==================== 信号学习 ====================
    
    /**
     * 开始学习信号
     * @param config 学习配置（可选）
     * @return 是否成功开始
     */
    bool startLearning(const LearningConfig& config = LearningConfig());
    
    /**
     * 停止学习信号
     * @return 是否成功停止
     */
    bool stopLearning();
    
    /**
     * 获取学习状态
     * @return 学习状态字符串
     */
    String getLearningStatus();
    
    /**
     * 获取学习进度
     * @return 进度百分比（0-100）
     */
    uint8_t getLearningProgress();
    
    /**
     * 保存学习结果
     * @param name 信号名称
     * @param description 信号描述
     * @param deviceType 设备类型
     * @return 操作结果
     */
    Result<SignalData> saveLearningResult(const String& name, const String& description = "", DeviceType deviceType = DeviceType::UNKNOWN);
    
    // ==================== 高级查询 ====================
    
    /**
     * 按协议搜索信号
     * @param protocol 红外协议
     * @return 信号列表
     */
    std::vector<SignalData> searchByProtocol(IRProtocol protocol);
    
    /**
     * 按设备类型搜索信号
     * @param deviceType 设备类型
     * @return 信号列表
     */
    std::vector<SignalData> searchByDeviceType(DeviceType deviceType);
    
    /**
     * 按名称搜索信号
     * @param namePattern 名称模式
     * @param caseSensitive 是否区分大小写
     * @return 信号列表
     */
    std::vector<SignalData> searchByName(const String& namePattern, bool caseSensitive = false);
    
    /**
     * 获取最常用的信号
     * @param limit 返回数量限制
     * @return 信号列表
     */
    std::vector<SignalData> getMostUsedSignals(size_t limit = 10);
    
    /**
     * 获取最近使用的信号
     * @param limit 返回数量限制
     * @return 信号列表
     */
    std::vector<SignalData> getRecentlyUsedSignals(size_t limit = 10);
    
    // ==================== 批量操作 ====================
    
    /**
     * 批量创建信号
     * @param signals 信号列表
     * @return 批量操作结果
     */
    BatchResult createSignalBatch(const std::vector<SignalData>& signals);
    
    /**
     * 批量删除信号
     * @param signalIds 信号ID列表
     * @return 批量操作结果
     */
    BatchResult deleteSignalBatch(const std::vector<SignalID>& signalIds);
    
    /**
     * 导入信号数据
     * @param jsonData JSON格式的信号数据
     * @return 批量操作结果
     */
    BatchResult importSignals(const String& jsonData);
    
    /**
     * 导出信号数据
     * @param signalIds 要导出的信号ID列表（空表示全部）
     * @return JSON格式的信号数据
     */
    String exportSignals(const std::vector<SignalID>& signalIds = {});
    
    // ==================== 统计和监控 ====================
    
    /**
     * 获取信号统计信息
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getStatistics(JsonDocument& doc);
    
    /**
     * 重置统计信息
     */
    void resetStatistics();
    
    /**
     * 获取服务状态
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getServiceStatus(JsonDocument& doc);
    
    // ==================== 配置管理 ====================
    
    /**
     * 更新学习配置
     * @param config 学习配置
     */
    void updateLearningConfig(const LearningConfig& config);
    
    /**
     * 更新发送配置
     * @param config 发送配置
     */
    void updateSendConfig(const SendConfig& config);
    
    /**
     * 获取学习配置
     * @return 学习配置
     */
    const LearningConfig& getLearningConfig() const { return m_learningConfig; }
    
    /**
     * 获取发送配置
     * @return 发送配置
     */
    const SendConfig& getSendConfig() const { return m_sendConfig; }
    
    // ==================== 回调设置 ====================
    
    /**
     * 设置信号学习完成回调
     * @param callback 回调函数
     */
    void setSignalLearnedCallback(std::function<void(const SignalData&)> callback);
    
    /**
     * 设置信号发送完成回调
     * @param callback 回调函数
     */
    void setSignalSentCallback(std::function<void(SignalID, bool)> callback);
    
    /**
     * 设置错误回调
     * @param callback 回调函数
     */
    void setErrorCallback(std::function<void(const String&)> callback);

private:
    // ==================== 内部实现方法 ====================
    
    /**
     * 验证信号数据
     * @param signal 信号数据
     * @return 验证结果
     */
    bool validateSignalData(const SignalData& signal);
    
    /**
     * 处理学习完成事件
     * @param result 学习结果
     */
    void onLearningComplete(const IRController::LearningResult& result);
    
    /**
     * 处理信号发送完成事件
     * @param success 是否成功
     */
    void onSignalSent(bool success);
    
    /**
     * 推送状态更新
     * @param signalId 信号ID
     * @param status 状态信息
     */
    void pushStatusUpdate(SignalID signalId, const String& status);
    
    /**
     * 记录错误
     * @param operation 操作名称
     * @param error 错误信息
     */
    void recordError(const String& operation, const String& error);
    
    /**
     * 更新使用统计
     * @param signalId 信号ID
     */
    void updateUsageStatistics(SignalID signalId);
};
