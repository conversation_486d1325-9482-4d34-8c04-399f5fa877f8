#include "SystemService.h"
#include <Arduino.h>
#include <esp_system.h>
#include <esp_chip_info.h>
#include <esp_flash.h>
#include <esp_task_wdt.h>

SystemService::SystemService(ConfigRepository* configRepository, WSManager* wsManager)
    : m_configRepository(configRepository), m_wsManager(wsManager),
      m_initialized(false), m_monitoringRunning(false), m_startTime(0),
      m_lastMonitorCheck(0), m_logIndex(0), m_totalLogEntries(0),
      m_systemResets(0), m_watchdogResets(0), m_performanceAlerts(0) {
    
    // 预分配日志条目
    m_logEntries.reserve(m_systemConfig.maxLogEntries);
}

SystemService::~SystemService() {
    cleanup();
}

bool SystemService::initialize() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        return true;
    }
    
    // 记录启动时间
    m_startTime = millis();
    m_lastMonitorCheck = m_startTime;
    
    // 加载系统配置
    if (m_configRepository && m_configRepository->isInitialized()) {
        // 从配置仓库加载系统配置
        // 这里可以实现配置加载逻辑
    }
    
    // 启用看门狗（如果配置启用）
    if (m_systemConfig.enableWatchdog) {
        enableWatchdog(m_systemConfig.watchdogTimeout);
    }
    
    // 启动性能监控（如果配置启用）
    if (m_systemConfig.enablePerformanceMonitor) {
        startPerformanceMonitoring();
    }
    
    m_initialized = true;
    
    // 记录初始化事件
    addLogEntry(LogLevel::INFO, "SystemService", "System service initialized successfully");
    
    Serial.println("✅ SystemService: Initialized successfully");
    return true;
}

void SystemService::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        // 停止性能监控
        stopPerformanceMonitoring();
        
        // 禁用看门狗
        disableWatchdog();
        
        // 记录关闭事件
        addLogEntry(LogLevel::INFO, "SystemService", "System service cleanup completed");
        
        m_initialized = false;
        
        Serial.println("✅ SystemService: Cleanup completed");
    }
}

SystemService::SystemStatus SystemService::getSystemStatus() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    SystemStatus status;
    
    status.initialized = m_initialized;
    status.uptime = getUptime();
    status.freeHeap = ESP.getFreeHeap();
    status.totalHeap = ESP.getHeapSize();
    status.minFreeHeap = ESP.getMinFreeHeap();
    status.cpuUsage = 0; // 需要实现CPU使用率计算
    status.temperature = temperatureRead();
    status.firmwareVersion = "1.0.0"; // 从编译时定义获取
    status.buildTime = __DATE__ " " __TIME__;
    
    return status;
}

SystemService::HardwareInfo SystemService::getHardwareInfo() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    HardwareInfo info;
    
    esp_chip_info_t chip_info;
    esp_chip_info(&chip_info);
    
    info.chipModel = ESP.getChipModel();
    info.chipRevision = chip_info.revision;
    info.chipId = ESP.getEfuseMac();
    info.flashSize = ESP.getFlashChipSize();
    info.flashSpeed = ESP.getFlashChipSpeed();
    info.macAddress = WiFi.macAddress();
    info.cpuFreq = ESP.getCpuFreqMHz();
    info.xtalFreq = getXtalFrequencyMhz();
    
    return info;
}

SystemService::PerformanceInfo SystemService::getPerformanceInfo() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    PerformanceInfo info;
    
    info.taskCount = uxTaskGetNumberOfTasks();
    info.taskStackHighWater = uxTaskGetStackHighWaterMark(NULL);
    info.interruptCount = 0; // 需要实现中断计数
    info.contextSwitches = 0; // 需要实现上下文切换计数
    info.memoryFragmentation = (ESP.getHeapSize() - ESP.getFreeHeap()) * 100 / ESP.getHeapSize();
    info.networkPackets = 0; // 需要从网络层获取
    info.storageOperations = 0; // 需要从存储层获取
    
    return info;
}

bool SystemService::checkSystemHealth() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return false;
    }
    
    bool healthy = true;
    
    // 检查内存状态
    if (!checkMemoryStatus()) {
        healthy = false;
        triggerAlert("Low memory warning");
    }
    
    // 检查温度状态
    if (!checkTemperatureStatus()) {
        healthy = false;
        triggerAlert("High temperature warning");
    }
    
    // 检查任务状态
    if (!checkTaskStatus()) {
        healthy = false;
        triggerAlert("Task status warning");
    }
    
    return healthy;
}

Timestamp SystemService::getUptime() {
    return millis() - m_startTime;
}

bool SystemService::softRestart(const String& reason) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    addLogEntry(LogLevel::WARNING, "SystemService", "Soft restart requested: " + reason);
    
    // 推送重启通知
    if (m_wsManager) {
        DynamicJsonDocument doc(512);
        doc["type"] = "system_restart";
        doc["reason"] = reason;
        doc["restart_type"] = "soft";
        doc["timestamp"] = millis();
        
        m_wsManager->broadcastJSONMessage(WSManager::MessageType::SYSTEM_STATUS, doc.as<JsonObject>());
    }
    
    m_systemResets++;
    
    // 延迟重启以确保消息发送
    delay(1000);
    
    ESP.restart();
    return true;
}

bool SystemService::hardRestart(const String& reason) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    addLogEntry(LogLevel::CRITICAL, "SystemService", "Hard restart requested: " + reason);
    
    // 推送重启通知
    if (m_wsManager) {
        DynamicJsonDocument doc(512);
        doc["type"] = "system_restart";
        doc["reason"] = reason;
        doc["restart_type"] = "hard";
        doc["timestamp"] = millis();
        
        m_wsManager->broadcastJSONMessage(WSManager::MessageType::SYSTEM_STATUS, doc.as<JsonObject>());
    }
    
    m_systemResets++;
    
    // 延迟重启以确保消息发送
    delay(1000);
    
    // 硬重启
    esp_restart();
    return true;
}

bool SystemService::factoryReset() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    addLogEntry(LogLevel::CRITICAL, "SystemService", "Factory reset requested");
    
    // 清除所有配置
    if (m_configRepository) {
        m_configRepository->clearAll();
    }
    
    // 清除文件系统
    // 这里可以实现文件系统清理逻辑
    
    // 推送恢复出厂设置通知
    if (m_wsManager) {
        DynamicJsonDocument doc(512);
        doc["type"] = "factory_reset";
        doc["timestamp"] = millis();
        
        m_wsManager->broadcastJSONMessage(WSManager::MessageType::SYSTEM_STATUS, doc.as<JsonObject>());
    }
    
    // 延迟重启
    delay(2000);
    
    ESP.restart();
    return true;
}

bool SystemService::enterDeepSleep(uint32_t duration) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    addLogEntry(LogLevel::INFO, "SystemService", "Entering deep sleep for " + String(duration) + "ms");
    
    // 推送深度睡眠通知
    if (m_wsManager) {
        DynamicJsonDocument doc(512);
        doc["type"] = "deep_sleep";
        doc["duration"] = duration;
        doc["timestamp"] = millis();
        
        m_wsManager->broadcastJSONMessage(WSManager::MessageType::SYSTEM_STATUS, doc.as<JsonObject>());
    }
    
    // 延迟以确保消息发送
    delay(1000);
    
    // 进入深度睡眠
    esp_sleep_enable_timer_wakeup(duration * 1000); // 转换为微秒
    esp_deep_sleep_start();
    
    return true;
}

bool SystemService::startPerformanceMonitoring() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return false;
    }
    
    if (m_monitoringRunning) {
        return true;
    }
    
    m_monitoringRunning = true;
    m_lastMonitorCheck = millis();
    
    addLogEntry(LogLevel::INFO, "SystemService", "Performance monitoring started");
    
    Serial.println("✅ SystemService: Performance monitoring started");
    return true;
}

bool SystemService::stopPerformanceMonitoring() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_monitoringRunning = false;
    
    addLogEntry(LogLevel::INFO, "SystemService", "Performance monitoring stopped");
    
    Serial.println("✅ SystemService: Performance monitoring stopped");
    return true;
}

JsonObject SystemService::getMemoryUsage(JsonDocument& doc) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    JsonObject memory = doc.createNestedObject("memory_usage");
    
    memory["free_heap"] = ESP.getFreeHeap();
    memory["total_heap"] = ESP.getHeapSize();
    memory["min_free_heap"] = ESP.getMinFreeHeap();
    memory["max_alloc_heap"] = ESP.getMaxAllocHeap();
    memory["heap_fragmentation"] = (ESP.getHeapSize() - ESP.getFreeHeap()) * 100 / ESP.getHeapSize();
    
    // PSRAM信息（如果有）
    if (ESP.getPsramSize() > 0) {
        memory["psram_size"] = ESP.getPsramSize();
        memory["free_psram"] = ESP.getFreePsram();
        memory["min_free_psram"] = ESP.getMinFreePsram();
        memory["max_alloc_psram"] = ESP.getMaxAllocPsram();
    }
    
    return memory;
}

JsonObject SystemService::getTaskInfo(JsonDocument& doc) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    JsonObject tasks = doc.createNestedObject("task_info");
    
    tasks["task_count"] = uxTaskGetNumberOfTasks();
    tasks["stack_high_water"] = uxTaskGetStackHighWaterMark(NULL);
    
    // 可以添加更详细的任务信息
    // 这需要遍历所有任务并获取其状态
    
    return tasks;
}

JsonObject SystemService::getNetworkStats(JsonDocument& doc) {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject network = doc.createNestedObject("network_stats");

    // WiFi状态
    network["wifi_connected"] = WiFi.isConnected();
    network["wifi_rssi"] = WiFi.RSSI();
    network["wifi_ssid"] = WiFi.SSID();
    network["local_ip"] = WiFi.localIP().toString();
    network["gateway_ip"] = WiFi.gatewayIP().toString();
    network["dns_ip"] = WiFi.dnsIP().toString();

    // 可以添加更多网络统计信息
    network["packets_sent"] = 0; // 需要从网络层获取
    network["packets_received"] = 0;
    network["bytes_sent"] = 0;
    network["bytes_received"] = 0;

    return network;
}

void SystemService::addLogEntry(LogLevel level, const String& component, const String& message, const String& details) {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查日志级别过滤
    if (level < m_systemConfig.minLogLevel) {
        return;
    }

    LogEntry entry;
    entry.timestamp = millis();
    entry.level = level;
    entry.component = component;
    entry.message = message;
    entry.details = details;

    // 循环缓冲区管理
    if (m_logEntries.size() < m_systemConfig.maxLogEntries) {
        m_logEntries.push_back(entry);
    } else {
        m_logEntries[m_logIndex] = entry;
        m_logIndex = (m_logIndex + 1) % m_systemConfig.maxLogEntries;
    }

    m_totalLogEntries++;

    // 调用日志条目回调
    if (m_logEntryCallback) {
        m_logEntryCallback(entry);
    }

    // 推送日志到WebSocket客户端
    if (m_wsManager && level >= LogLevel::WARNING) {
        DynamicJsonDocument doc(1024);
        doc["type"] = "log_entry";
        doc["timestamp"] = entry.timestamp;
        doc["level"] = logLevelToString(level);
        doc["component"] = component;
        doc["message"] = message;
        if (!details.isEmpty()) {
            doc["details"] = details;
        }

        m_wsManager->broadcastJSONMessage(WSManager::MessageType::ERROR_NOTIFICATION, doc.as<JsonObject>());
    }
}

std::vector<SystemService::LogEntry> SystemService::getLogEntries(size_t maxEntries, LogLevel minLevel) {
    std::lock_guard<std::mutex> lock(m_mutex);

    std::vector<LogEntry> result;
    result.reserve(std::min(maxEntries, m_logEntries.size()));

    // 从最新的日志开始
    size_t count = 0;
    for (auto it = m_logEntries.rbegin(); it != m_logEntries.rend() && count < maxEntries; ++it) {
        if (it->level >= minLevel) {
            result.push_back(*it);
            count++;
        }
    }

    return result;
}

size_t SystemService::clearLogs() {
    std::lock_guard<std::mutex> lock(m_mutex);

    size_t clearedCount = m_logEntries.size();
    m_logEntries.clear();
    m_logIndex = 0;

    addLogEntry(LogLevel::INFO, "SystemService", "Log entries cleared", "Cleared " + String(clearedCount) + " entries");

    return clearedCount;
}

String SystemService::exportLogs(const String& format) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (format == "json") {
        DynamicJsonDocument doc(m_logEntries.size() * 256 + 1024);
        JsonArray logsArray = doc.createNestedArray("logs");

        for (const auto& entry : m_logEntries) {
            JsonObject logObj = logsArray.createNestedObject();
            logObj["timestamp"] = entry.timestamp;
            logObj["level"] = logLevelToString(entry.level);
            logObj["component"] = entry.component;
            logObj["message"] = entry.message;
            if (!entry.details.isEmpty()) {
                logObj["details"] = entry.details;
            }
        }

        doc["total_entries"] = m_logEntries.size();
        doc["export_time"] = millis();

        String result;
        serializeJson(doc, result);
        return result;
    } else {
        // 文本格式
        String result;
        result.reserve(m_logEntries.size() * 100);

        for (const auto& entry : m_logEntries) {
            result += String(entry.timestamp) + " [" + logLevelToString(entry.level) + "] " +
                     entry.component + ": " + entry.message;
            if (!entry.details.isEmpty()) {
                result += " (" + entry.details + ")";
            }
            result += "\n";
        }

        return result;
    }
}

bool SystemService::saveLogsToFile(const String& filename) {
    // 这里需要实现文件系统操作
    // 暂时返回false表示未实现
    addLogEntry(LogLevel::WARNING, "SystemService", "Save logs to file not implemented", filename);
    return false;
}

bool SystemService::updateSystemConfig(const SystemConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);

    SystemConfig oldConfig = m_systemConfig;
    m_systemConfig = config;

    // 重新分配日志缓冲区（如果大小改变）
    if (oldConfig.maxLogEntries != config.maxLogEntries) {
        if (config.maxLogEntries < m_logEntries.size()) {
            // 缩小缓冲区，保留最新的条目
            std::vector<LogEntry> newEntries;
            newEntries.reserve(config.maxLogEntries);

            size_t startIndex = m_logEntries.size() - config.maxLogEntries;
            for (size_t i = startIndex; i < m_logEntries.size(); i++) {
                newEntries.push_back(m_logEntries[i]);
            }

            m_logEntries = std::move(newEntries);
            m_logIndex = 0;
        } else {
            m_logEntries.reserve(config.maxLogEntries);
        }
    }

    // 更新看门狗设置
    if (oldConfig.enableWatchdog != config.enableWatchdog) {
        if (config.enableWatchdog) {
            enableWatchdog(config.watchdogTimeout);
        } else {
            disableWatchdog();
        }
    } else if (config.enableWatchdog && oldConfig.watchdogTimeout != config.watchdogTimeout) {
        enableWatchdog(config.watchdogTimeout);
    }

    // 更新性能监控设置
    if (oldConfig.enablePerformanceMonitor != config.enablePerformanceMonitor) {
        if (config.enablePerformanceMonitor) {
            startPerformanceMonitoring();
        } else {
            stopPerformanceMonitoring();
        }
    }

    // 保存配置到仓库
    if (m_configRepository) {
        // 这里可以实现配置保存逻辑
    }

    addLogEntry(LogLevel::INFO, "SystemService", "System configuration updated");

    return true;
}

String SystemService::backupConfiguration() {
    std::lock_guard<std::mutex> lock(m_mutex);

    DynamicJsonDocument doc(2048);

    // 系统配置
    JsonObject sysConfig = doc.createNestedObject("system_config");
    sysConfig["enable_watchdog"] = m_systemConfig.enableWatchdog;
    sysConfig["watchdog_timeout"] = m_systemConfig.watchdogTimeout;
    sysConfig["enable_performance_monitor"] = m_systemConfig.enablePerformanceMonitor;
    sysConfig["monitor_interval"] = m_systemConfig.monitorInterval;
    sysConfig["enable_auto_restart"] = m_systemConfig.enableAutoRestart;
    sysConfig["max_log_entries"] = m_systemConfig.maxLogEntries;
    sysConfig["min_log_level"] = static_cast<int>(m_systemConfig.minLogLevel);

    // 添加备份时间戳
    doc["backup_timestamp"] = millis();
    doc["firmware_version"] = "1.0.0";

    String result;
    serializeJson(doc, result);

    addLogEntry(LogLevel::INFO, "SystemService", "Configuration backup created");

    return result;
}

bool SystemService::restoreConfiguration(const String& configJson) {
    std::lock_guard<std::mutex> lock(m_mutex);

    DynamicJsonDocument doc(2048);
    DeserializationError error = deserializeJson(doc, configJson);

    if (error) {
        addLogEntry(LogLevel::ERROR, "SystemService", "Failed to parse configuration JSON", error.c_str());
        return false;
    }

    // 恢复系统配置
    if (doc.containsKey("system_config")) {
        JsonObject sysConfig = doc["system_config"];

        SystemConfig newConfig;
        newConfig.enableWatchdog = sysConfig["enable_watchdog"] | m_systemConfig.enableWatchdog;
        newConfig.watchdogTimeout = sysConfig["watchdog_timeout"] | m_systemConfig.watchdogTimeout;
        newConfig.enablePerformanceMonitor = sysConfig["enable_performance_monitor"] | m_systemConfig.enablePerformanceMonitor;
        newConfig.monitorInterval = sysConfig["monitor_interval"] | m_systemConfig.monitorInterval;
        newConfig.enableAutoRestart = sysConfig["enable_auto_restart"] | m_systemConfig.enableAutoRestart;
        newConfig.maxLogEntries = sysConfig["max_log_entries"] | m_systemConfig.maxLogEntries;
        newConfig.minLogLevel = static_cast<LogLevel>(sysConfig["min_log_level"] | static_cast<int>(m_systemConfig.minLogLevel));

        updateSystemConfig(newConfig);
    }

    addLogEntry(LogLevel::INFO, "SystemService", "Configuration restored successfully");

    return true;
}

bool SystemService::enableWatchdog(uint32_t timeout) {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 启用ESP32看门狗
    esp_task_wdt_config_t wdt_config = {
        .timeout_ms = timeout,
        .idle_core_mask = (1 << portNUM_PROCESSORS) - 1,
        .trigger_panic = true
    };

    esp_err_t result = esp_task_wdt_init(&wdt_config);
    if (result == ESP_OK) {
        esp_task_wdt_add(NULL); // 添加当前任务到看门狗

        addLogEntry(LogLevel::INFO, "SystemService", "Watchdog enabled", "Timeout: " + String(timeout) + "ms");

        Serial.printf("✅ SystemService: Watchdog enabled with timeout %dms\n", timeout);
        return true;
    } else {
        addLogEntry(LogLevel::ERROR, "SystemService", "Failed to enable watchdog", "Error: " + String(result));
        return false;
    }
}

bool SystemService::disableWatchdog() {
    std::lock_guard<std::mutex> lock(m_mutex);

    esp_task_wdt_delete(NULL); // 从看门狗移除当前任务
    esp_task_wdt_deinit();

    addLogEntry(LogLevel::INFO, "SystemService", "Watchdog disabled");

    Serial.println("✅ SystemService: Watchdog disabled");
    return true;
}

void SystemService::feedWatchdog() {
    if (m_systemConfig.enableWatchdog) {
        esp_task_wdt_reset();
    }
}

JsonObject SystemService::getSystemStatistics(JsonDocument& doc) {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject stats = doc.createNestedObject("system_statistics");

    stats["initialized"] = m_initialized;
    stats["monitoring_running"] = m_monitoringRunning;
    stats["uptime"] = getUptime();
    stats["total_log_entries"] = m_totalLogEntries;
    stats["current_log_entries"] = m_logEntries.size();
    stats["system_resets"] = m_systemResets;
    stats["watchdog_resets"] = m_watchdogResets;
    stats["performance_alerts"] = m_performanceAlerts;

    // 系统状态
    SystemStatus status = getSystemStatus();
    JsonObject statusObj = stats.createNestedObject("current_status");
    statusObj["free_heap"] = status.freeHeap;
    statusObj["total_heap"] = status.totalHeap;
    statusObj["min_free_heap"] = status.minFreeHeap;
    statusObj["cpu_usage"] = status.cpuUsage;
    statusObj["temperature"] = status.temperature;

    return stats;
}

void SystemService::resetStatistics() {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_totalLogEntries = 0;
    m_systemResets = 0;
    m_watchdogResets = 0;
    m_performanceAlerts = 0;

    addLogEntry(LogLevel::INFO, "SystemService", "Statistics reset");

    Serial.println("✅ SystemService: Statistics reset");
}

JsonObject SystemService::getServiceStatus(JsonDocument& doc) {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject status = doc.createNestedObject("system_service_status");

    status["initialized"] = m_initialized;
    status["monitoring_running"] = m_monitoringRunning;
    status["start_time"] = m_startTime;
    status["last_monitor_check"] = m_lastMonitorCheck;
    status["log_entries_count"] = m_logEntries.size();
    status["log_index"] = m_logIndex;

    // 配置信息
    JsonObject config = status.createNestedObject("config");
    config["enable_watchdog"] = m_systemConfig.enableWatchdog;
    config["watchdog_timeout"] = m_systemConfig.watchdogTimeout;
    config["enable_performance_monitor"] = m_systemConfig.enablePerformanceMonitor;
    config["monitor_interval"] = m_systemConfig.monitorInterval;
    config["enable_auto_restart"] = m_systemConfig.enableAutoRestart;
    config["max_log_entries"] = m_systemConfig.maxLogEntries;
    config["min_log_level"] = static_cast<int>(m_systemConfig.minLogLevel);

    return status;
}

void SystemService::setStatusChangedCallback(std::function<void(const SystemStatus&)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_statusChangedCallback = callback;
}

void SystemService::setLogEntryCallback(std::function<void(const LogEntry&)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_logEntryCallback = callback;
}

void SystemService::setAlertCallback(std::function<void(const String&)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_alertCallback = callback;
}

void SystemService::loop() {
    if (!m_initialized) {
        return;
    }

    // 喂狗
    feedWatchdog();

    // 处理性能监控
    if (m_monitoringRunning) {
        processPerformanceMonitoring();
    }
}

// ==================== 内部实现方法 ====================

void SystemService::processPerformanceMonitoring() {
    Timestamp currentTime = millis();

    // 检查监控间隔
    if (currentTime - m_lastMonitorCheck < m_systemConfig.monitorInterval) {
        return;
    }

    m_lastMonitorCheck = currentTime;

    // 检查系统健康状态
    bool healthy = checkSystemHealth();

    // 获取当前系统状态
    SystemStatus status = getSystemStatus();

    // 推送状态更新
    pushSystemStatusUpdate(status);

    // 调用状态变化回调
    if (m_statusChangedCallback) {
        m_statusChangedCallback(status);
    }

    if (!healthy) {
        m_performanceAlerts++;
    }
}

bool SystemService::checkMemoryStatus() {
    uint32_t freeHeap = ESP.getFreeHeap();
    uint32_t totalHeap = ESP.getHeapSize();

    // 检查可用内存是否低于20%
    if (freeHeap < totalHeap * 0.2) {
        addLogEntry(LogLevel::WARNING, "SystemService", "Low memory warning",
                   "Free: " + String(freeHeap) + " / Total: " + String(totalHeap));
        return false;
    }

    return true;
}

bool SystemService::checkTemperatureStatus() {
    float temperature = temperatureRead();

    // 检查温度是否过高（超过80度）
    if (temperature > 80.0f) {
        addLogEntry(LogLevel::WARNING, "SystemService", "High temperature warning",
                   "Temperature: " + String(temperature) + "°C");
        return false;
    }

    return true;
}

bool SystemService::checkTaskStatus() {
    uint32_t stackHighWater = uxTaskGetStackHighWaterMark(NULL);

    // 检查栈使用情况（如果剩余栈空间小于512字节）
    if (stackHighWater < 512) {
        addLogEntry(LogLevel::WARNING, "SystemService", "Low stack warning",
                   "Stack high water: " + String(stackHighWater) + " bytes");
        return false;
    }

    return true;
}

void SystemService::pushSystemStatusUpdate(const SystemStatus& status) {
    if (m_wsManager) {
        DynamicJsonDocument doc(1024);

        JsonObject statusObj = doc.createNestedObject("system_status");
        statusObj["initialized"] = status.initialized;
        statusObj["uptime"] = status.uptime;
        statusObj["free_heap"] = status.freeHeap;
        statusObj["total_heap"] = status.totalHeap;
        statusObj["min_free_heap"] = status.minFreeHeap;
        statusObj["cpu_usage"] = status.cpuUsage;
        statusObj["temperature"] = status.temperature;
        statusObj["firmware_version"] = status.firmwareVersion;
        statusObj["build_time"] = status.buildTime;
        statusObj["timestamp"] = millis();

        m_wsManager->broadcastJSONMessage(WSManager::MessageType::SYSTEM_STATUS, doc.as<JsonObject>());
    }
}

void SystemService::pushHardwareStatusUpdate(const HardwareInfo& info) {
    if (m_wsManager) {
        DynamicJsonDocument doc(1024);

        JsonObject hardwareObj = doc.createNestedObject("hardware_info");
        hardwareObj["chip_model"] = info.chipModel;
        hardwareObj["chip_revision"] = info.chipRevision;
        hardwareObj["chip_id"] = String(info.chipId, HEX);
        hardwareObj["flash_size"] = info.flashSize;
        hardwareObj["flash_speed"] = info.flashSpeed;
        hardwareObj["mac_address"] = info.macAddress;
        hardwareObj["cpu_freq"] = info.cpuFreq;
        hardwareObj["xtal_freq"] = info.xtalFreq;
        hardwareObj["timestamp"] = millis();

        m_wsManager->broadcastJSONMessage(WSManager::MessageType::HARDWARE_STATUS, doc.as<JsonObject>());
    }
}

void SystemService::recordSystemEvent(const String& event, LogLevel level) {
    addLogEntry(level, "SystemService", event);
}

void SystemService::triggerAlert(const String& alert) {
    addLogEntry(LogLevel::WARNING, "SystemService", "System alert", alert);

    // 调用警报回调
    if (m_alertCallback) {
        m_alertCallback(alert);
    }

    // 推送警报到WebSocket客户端
    if (m_wsManager) {
        DynamicJsonDocument doc(512);
        doc["type"] = "system_alert";
        doc["alert"] = alert;
        doc["timestamp"] = millis();

        m_wsManager->broadcastJSONMessage(WSManager::MessageType::ERROR_NOTIFICATION, doc.as<JsonObject>());
    }
}

String SystemService::logLevelToString(LogLevel level) const {
    switch (level) {
        case LogLevel::DEBUG: return "DEBUG";
        case LogLevel::INFO: return "INFO";
        case LogLevel::WARNING: return "WARNING";
        case LogLevel::ERROR: return "ERROR";
        case LogLevel::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

SystemService::LogLevel SystemService::stringToLogLevel(const String& levelStr) const {
    if (levelStr == "DEBUG") return LogLevel::DEBUG;
    if (levelStr == "INFO") return LogLevel::INFO;
    if (levelStr == "WARNING") return LogLevel::WARNING;
    if (levelStr == "ERROR") return LogLevel::ERROR;
    if (levelStr == "CRITICAL") return LogLevel::CRITICAL;
    return LogLevel::INFO; // 默认级别
}
