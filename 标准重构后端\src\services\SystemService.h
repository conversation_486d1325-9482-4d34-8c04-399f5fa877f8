#pragma once

#include "../core/DataStructures.h"
#include "../data/ConfigRepository.h"
#include "../network/WSManager.h"
#include <functional>
#include <mutex>
#include <vector>

/**
 * ESP32-S3 红外控制系统 - 系统业务服务
 * 
 * 功能：
 * 1. 系统状态监控和管理
 * 2. 硬件信息获取和检测
 * 3. 系统性能监控
 * 4. 系统日志管理
 * 5. 系统重置和恢复
 * 6. 配置管理和备份
 * 
 * 设计原则：
 * - 状态监控：实时系统状态检测
 * - 性能优化：系统资源监控和优化
 * - 安全管理：系统安全检查和保护
 * - 日志管理：完整的日志记录和查询
 * - 配置管理：系统配置的备份和恢复
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 */

class SystemService {
public:
    // 系统状态结构
    struct SystemStatus {
        bool initialized;              // 是否已初始化
        Timestamp uptime;              // 运行时间
        uint32_t freeHeap;             // 可用堆内存
        uint32_t totalHeap;            // 总堆内存
        uint32_t minFreeHeap;          // 最小可用堆内存
        uint8_t cpuUsage;              // CPU使用率
        float temperature;             // 芯片温度
        String firmwareVersion;        // 固件版本
        String buildTime;              // 编译时间
        
        SystemStatus() : initialized(false), uptime(0), freeHeap(0), totalHeap(0),
                        minFreeHeap(0), cpuUsage(0), temperature(0.0f) {}
    };
    
    // 硬件信息结构
    struct HardwareInfo {
        String chipModel;              // 芯片型号
        uint8_t chipRevision;          // 芯片版本
        uint32_t chipId;               // 芯片ID
        uint32_t flashSize;            // Flash大小
        uint32_t flashSpeed;           // Flash速度
        String macAddress;             // MAC地址
        uint32_t cpuFreq;              // CPU频率
        uint32_t xtalFreq;             // 晶振频率
        
        HardwareInfo() : chipRevision(0), chipId(0), flashSize(0), flashSpeed(0),
                        cpuFreq(0), xtalFreq(0) {}
    };
    
    // 性能信息结构
    struct PerformanceInfo {
        uint32_t taskCount;            // 任务数量
        uint32_t taskStackHighWater;   // 任务栈高水位
        uint32_t interruptCount;       // 中断计数
        uint32_t contextSwitches;      // 上下文切换次数
        uint32_t memoryFragmentation;  // 内存碎片化程度
        uint32_t networkPackets;       // 网络包数量
        uint32_t storageOperations;    // 存储操作次数
        
        PerformanceInfo() : taskCount(0), taskStackHighWater(0), interruptCount(0),
                           contextSwitches(0), memoryFragmentation(0),
                           networkPackets(0), storageOperations(0) {}
    };
    
    // 日志级别枚举
    enum class LogLevel {
        DEBUG = 0,
        INFO = 1,
        WARNING = 2,
        ERROR = 3,
        CRITICAL = 4
    };
    
    // 日志条目结构
    struct LogEntry {
        Timestamp timestamp;           // 时间戳
        LogLevel level;                // 日志级别
        String component;              // 组件名称
        String message;                // 日志消息
        String details;                // 详细信息
        
        LogEntry() : timestamp(0), level(LogLevel::INFO) {}
    };
    
    // 系统配置结构
    struct SystemConfig {
        bool enableWatchdog;           // 是否启用看门狗
        uint32_t watchdogTimeout;      // 看门狗超时时间
        bool enablePerformanceMonitor; // 是否启用性能监控
        uint32_t monitorInterval;      // 监控间隔
        bool enableAutoRestart;        // 是否启用自动重启
        uint32_t maxLogEntries;        // 最大日志条目数
        LogLevel minLogLevel;          // 最小日志级别
        
        SystemConfig() : enableWatchdog(true), watchdogTimeout(30000),
                        enablePerformanceMonitor(true), monitorInterval(5000),
                        enableAutoRestart(false), maxLogEntries(1000),
                        minLogLevel(LogLevel::INFO) {}
    };

private:
    // 依赖组件
    ConfigRepository* m_configRepository;
    WSManager* m_wsManager;
    
    // 配置
    SystemConfig m_systemConfig;
    
    // 状态
    bool m_initialized;
    bool m_monitoringRunning;
    Timestamp m_startTime;
    Timestamp m_lastMonitorCheck;
    
    // 日志管理
    std::vector<LogEntry> m_logEntries;
    size_t m_logIndex;
    
    // 统计信息
    uint32_t m_totalLogEntries;
    uint32_t m_systemResets;
    uint32_t m_watchdogResets;
    uint32_t m_performanceAlerts;
    
    // 线程安全
    mutable std::mutex m_mutex;
    
    // 回调函数
    std::function<void(const SystemStatus&)> m_statusChangedCallback;
    std::function<void(const LogEntry&)> m_logEntryCallback;
    std::function<void(const String&)> m_alertCallback;

public:
    /**
     * 构造函数
     * @param configRepository 配置仓库
     * @param wsManager WebSocket管理器
     */
    SystemService(ConfigRepository* configRepository, WSManager* wsManager);
    
    /**
     * 析构函数
     */
    ~SystemService();
    
    // ==================== 生命周期管理 ====================
    
    /**
     * 初始化系统服务
     * @return 是否成功
     */
    bool initialize();
    
    /**
     * 清理资源
     */
    void cleanup();
    
    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return m_initialized; }
    
    // ==================== 系统状态管理 ====================
    
    /**
     * 获取系统状态
     * @return 系统状态
     */
    SystemStatus getSystemStatus();
    
    /**
     * 获取硬件信息
     * @return 硬件信息
     */
    HardwareInfo getHardwareInfo();
    
    /**
     * 获取性能信息
     * @return 性能信息
     */
    PerformanceInfo getPerformanceInfo();
    
    /**
     * 检查系统健康状态
     * @return 是否健康
     */
    bool checkSystemHealth();
    
    /**
     * 获取系统运行时间
     * @return 运行时间（毫秒）
     */
    Timestamp getUptime();
    
    // ==================== 系统控制 ====================
    
    /**
     * 系统软重启
     * @param reason 重启原因
     * @return 是否成功
     */
    bool softRestart(const String& reason = "Manual restart");
    
    /**
     * 系统硬重启
     * @param reason 重启原因
     * @return 是否成功
     */
    bool hardRestart(const String& reason = "Manual restart");
    
    /**
     * 恢复出厂设置
     * @return 是否成功
     */
    bool factoryReset();
    
    /**
     * 进入深度睡眠
     * @param duration 睡眠时间（毫秒）
     * @return 是否成功
     */
    bool enterDeepSleep(uint32_t duration);
    
    // ==================== 性能监控 ====================
    
    /**
     * 启动性能监控
     * @return 是否成功
     */
    bool startPerformanceMonitoring();
    
    /**
     * 停止性能监控
     * @return 是否成功
     */
    bool stopPerformanceMonitoring();
    
    /**
     * 获取内存使用情况
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getMemoryUsage(JsonDocument& doc);
    
    /**
     * 获取任务信息
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getTaskInfo(JsonDocument& doc);
    
    /**
     * 获取网络统计
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getNetworkStats(JsonDocument& doc);
    
    // ==================== 日志管理 ====================
    
    /**
     * 添加日志条目
     * @param level 日志级别
     * @param component 组件名称
     * @param message 日志消息
     * @param details 详细信息
     */
    void addLogEntry(LogLevel level, const String& component, const String& message, const String& details = "");
    
    /**
     * 获取日志条目
     * @param maxEntries 最大条目数
     * @param minLevel 最小日志级别
     * @return 日志条目列表
     */
    std::vector<LogEntry> getLogEntries(size_t maxEntries = 100, LogLevel minLevel = LogLevel::DEBUG);
    
    /**
     * 清空日志
     * @return 清空的条目数
     */
    size_t clearLogs();
    
    /**
     * 导出日志
     * @param format 导出格式（json/text）
     * @return 日志数据
     */
    String exportLogs(const String& format = "json");
    
    /**
     * 保存日志到文件
     * @param filename 文件名
     * @return 是否成功
     */
    bool saveLogsToFile(const String& filename);
    
    // ==================== 配置管理 ====================
    
    /**
     * 获取系统配置
     * @return 系统配置
     */
    const SystemConfig& getSystemConfig() const { return m_systemConfig; }
    
    /**
     * 更新系统配置
     * @param config 新配置
     * @return 是否成功
     */
    bool updateSystemConfig(const SystemConfig& config);
    
    /**
     * 备份系统配置
     * @return 配置JSON字符串
     */
    String backupConfiguration();
    
    /**
     * 恢复系统配置
     * @param configJson 配置JSON字符串
     * @return 是否成功
     */
    bool restoreConfiguration(const String& configJson);
    
    // ==================== 看门狗管理 ====================
    
    /**
     * 启用看门狗
     * @param timeout 超时时间（毫秒）
     * @return 是否成功
     */
    bool enableWatchdog(uint32_t timeout);
    
    /**
     * 禁用看门狗
     * @return 是否成功
     */
    bool disableWatchdog();
    
    /**
     * 喂狗操作
     */
    void feedWatchdog();
    
    // ==================== 统计和监控 ====================
    
    /**
     * 获取系统统计信息
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getSystemStatistics(JsonDocument& doc);
    
    /**
     * 重置统计信息
     */
    void resetStatistics();
    
    /**
     * 获取服务状态
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getServiceStatus(JsonDocument& doc);

    // ==================== 回调设置 ====================

    /**
     * 设置状态变化回调
     * @param callback 回调函数
     */
    void setStatusChangedCallback(std::function<void(const SystemStatus&)> callback);

    /**
     * 设置日志条目回调
     * @param callback 回调函数
     */
    void setLogEntryCallback(std::function<void(const LogEntry&)> callback);

    /**
     * 设置警报回调
     * @param callback 回调函数
     */
    void setAlertCallback(std::function<void(const String&)> callback);

    // ==================== 循环处理 ====================

    /**
     * 主循环处理函数（需要在主循环中调用）
     */
    void loop();

private:
    // ==================== 内部实现方法 ====================

    /**
     * 处理性能监控
     */
    void processPerformanceMonitoring();

    /**
     * 检查内存状态
     * @return 是否正常
     */
    bool checkMemoryStatus();

    /**
     * 检查温度状态
     * @return 是否正常
     */
    bool checkTemperatureStatus();

    /**
     * 检查任务状态
     * @return 是否正常
     */
    bool checkTaskStatus();

    /**
     * 推送系统状态更新
     * @param status 系统状态
     */
    void pushSystemStatusUpdate(const SystemStatus& status);

    /**
     * 推送硬件状态更新
     * @param info 硬件信息
     */
    void pushHardwareStatusUpdate(const HardwareInfo& info);

    /**
     * 记录系统事件
     * @param event 事件描述
     * @param level 日志级别
     */
    void recordSystemEvent(const String& event, LogLevel level = LogLevel::INFO);

    /**
     * 触发系统警报
     * @param alert 警报信息
     */
    void triggerAlert(const String& alert);

    /**
     * 日志级别转字符串
     * @param level 日志级别
     * @return 字符串表示
     */
    String logLevelToString(LogLevel level) const;

    /**
     * 字符串转日志级别
     * @param levelStr 级别字符串
     * @return 日志级别
     */
    LogLevel stringToLogLevel(const String& levelStr) const;
};
