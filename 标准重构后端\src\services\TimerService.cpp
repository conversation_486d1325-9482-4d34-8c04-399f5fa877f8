#include "TimerService.h"
#include "../core/IDGenerator.h"
#include <Arduino.h>

TimerService::TimerService(TimerRepository* timerRepository, TaskRepository* taskRepository,
                          TaskService* taskService, WSManager* wsManager)
    : m_timerRepository(timerRepository), m_taskRepository(taskRepository),
      m_taskService(taskService), m_wsManager(wsManager),
      m_initialized(false), m_triggerCheckRunning(false), m_lastTriggerCheck(0),
      m_lastCleanupTime(0), m_totalTimersTriggered(0), m_totalBatchOperations(0),
      m_triggerFailures(0), m_timeValidationFailures(0) {
}

TimerService::~TimerService() {
    cleanup();
}

bool TimerService::initialize() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        return true;
    }
    
    // 验证依赖组件
    if (!m_timerRepository || !m_taskRepository || !m_taskService) {
        Serial.println("❌ TimerService: Missing required dependencies");
        return false;
    }
    
    // 检查依赖组件是否已初始化
    if (!m_timerRepository->isInitialized() || !m_taskRepository->isInitialized() || 
        !m_taskService->isInitialized()) {
        Serial.println("❌ TimerService: Dependencies not initialized");
        return false;
    }
    
    // 初始化时间
    m_lastTriggerCheck = millis();
    m_lastCleanupTime = millis();
    
    m_initialized = true;
    
    Serial.println("✅ TimerService: Initialized successfully");
    return true;
}

void TimerService::cleanup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_initialized) {
        // 停止触发检查
        stopTriggerCheck();
        
        m_initialized = false;
        
        Serial.println("✅ TimerService: Cleanup completed");
    }
}

Result<TimerData> TimerService::createTimer(const TimerData& timer) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return Result<TimerData>::Error(ErrorCode::SYSTEM_INITIALIZATION_FAILED, "Service not initialized");
    }
    
    // 验证定时器数据
    if (!validateTimerData(timer)) {
        recordError("createTimer", "Invalid timer data");
        return Result<TimerData>::Error(ErrorCode::INVALID_PARAMETER, "Invalid timer data");
    }
    
    // 验证时间设置
    auto timeValidation = validateTimerTime(timer);
    if (!timeValidation.isValid) {
        recordError("createTimer", "Invalid timer time: " + timeValidation.errorMessage);
        m_timeValidationFailures++;
        return Result<TimerData>::Error(ErrorCode::INVALID_PARAMETER, "Invalid timer time: " + timeValidation.errorMessage);
    }
    
    // 创建定时器
    auto result = m_timerRepository->create(timer);
    if (result.isSuccess()) {
        // 推送状态更新
        pushStatusUpdate(result.getValue().id, result.getValue().enabled);
        
        Serial.printf("✅ TimerService: Timer created - ID:%d, Name:%s\n", 
                     result.getValue().id, result.getValue().name.c_str());
    } else {
        recordError("createTimer", result.getErrorMessage());
    }
    
    return result;
}

Result<TimerData> TimerService::getTimer(TimerID timerId) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return Result<TimerData>::Error(ErrorCode::SYSTEM_INITIALIZATION_FAILED, "Service not initialized");
    }
    
    auto result = m_timerRepository->getById(timerId);
    if (!result.isSuccess()) {
        recordError("getTimer", "Timer not found: " + String(timerId));
    }
    
    return result;
}

Result<TimerData> TimerService::updateTimer(const TimerData& timer) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return Result<TimerData>::Error(ErrorCode::SYSTEM_INITIALIZATION_FAILED, "Service not initialized");
    }
    
    // 验证定时器数据
    if (!validateTimerData(timer)) {
        recordError("updateTimer", "Invalid timer data");
        return Result<TimerData>::Error(ErrorCode::INVALID_PARAMETER, "Invalid timer data");
    }
    
    // 验证时间设置
    auto timeValidation = validateTimerTime(timer);
    if (!timeValidation.isValid) {
        recordError("updateTimer", "Invalid timer time: " + timeValidation.errorMessage);
        m_timeValidationFailures++;
        return Result<TimerData>::Error(ErrorCode::INVALID_PARAMETER, "Invalid timer time: " + timeValidation.errorMessage);
    }
    
    // 更新定时器
    auto result = m_timerRepository->update(timer);
    if (result.isSuccess()) {
        // 推送状态更新
        pushStatusUpdate(timer.id, timer.enabled);
        
        Serial.printf("✅ TimerService: Timer updated - ID:%d, Name:%s\n", 
                     timer.id, timer.name.c_str());
    } else {
        recordError("updateTimer", result.getErrorMessage());
    }
    
    return result;
}

bool TimerService::deleteTimer(TimerID timerId) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return false;
    }
    
    bool success = m_timerRepository->deleteById(timerId);
    if (success) {
        // 推送状态更新
        pushStatusUpdate(timerId, false);
        
        Serial.printf("✅ TimerService: Timer deleted - ID:%d\n", timerId);
    } else {
        recordError("deleteTimer", "Failed to delete timer: " + String(timerId));
    }
    
    return success;
}

std::vector<TimerData> TimerService::getAllTimers() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return {};
    }
    
    return m_timerRepository->getAll();
}

bool TimerService::enableTimer(TimerID timerId) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return false;
    }
    
    // 获取定时器
    auto timerResult = m_timerRepository->getById(timerId);
    if (!timerResult.isSuccess()) {
        recordError("enableTimer", "Timer not found: " + String(timerId));
        return false;
    }
    
    TimerData timer = timerResult.getValue();
    timer.enabled = true;
    
    // 重新计算下次触发时间
    timer.nextTriggerTime = calculateNextTriggerTime(timer);
    
    // 更新定时器
    auto updateResult = m_timerRepository->update(timer);
    if (updateResult.isSuccess()) {
        pushStatusUpdate(timerId, true);
        
        Serial.printf("✅ TimerService: Timer enabled - ID:%d, NextTrigger:%d\n", 
                     timerId, timer.nextTriggerTime);
        return true;
    } else {
        recordError("enableTimer", updateResult.getErrorMessage());
        return false;
    }
}

bool TimerService::disableTimer(TimerID timerId) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return false;
    }
    
    // 获取定时器
    auto timerResult = m_timerRepository->getById(timerId);
    if (!timerResult.isSuccess()) {
        recordError("disableTimer", "Timer not found: " + String(timerId));
        return false;
    }
    
    TimerData timer = timerResult.getValue();
    timer.enabled = false;
    timer.nextTriggerTime = 0;
    
    // 更新定时器
    auto updateResult = m_timerRepository->update(timer);
    if (updateResult.isSuccess()) {
        pushStatusUpdate(timerId, false);
        
        Serial.printf("✅ TimerService: Timer disabled - ID:%d\n", timerId);
        return true;
    } else {
        recordError("disableTimer", updateResult.getErrorMessage());
        return false;
    }
}

TimerService::TriggerResult TimerService::triggerTimer(TimerID timerId) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    TriggerResult result;
    result.timerId = timerId;
    result.triggerTime = millis();
    
    if (!m_initialized) {
        result.errorMessage = "Service not initialized";
        recordError("triggerTimer", result.errorMessage);
        return result;
    }
    
    // 获取定时器
    auto timerResult = m_timerRepository->getById(timerId);
    if (!timerResult.isSuccess()) {
        result.errorMessage = "Timer not found: " + String(timerId);
        recordError("triggerTimer", result.errorMessage);
        return result;
    }
    
    return processTimerTrigger(timerResult.getValue());
}

bool TimerService::resetTimer(TimerID timerId) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return false;
    }
    
    // 获取定时器
    auto timerResult = m_timerRepository->getById(timerId);
    if (!timerResult.isSuccess()) {
        recordError("resetTimer", "Timer not found: " + String(timerId));
        return false;
    }
    
    TimerData timer = timerResult.getValue();
    
    // 重新计算下次触发时间
    timer.nextTriggerTime = calculateNextTriggerTime(timer);
    
    // 更新定时器
    auto updateResult = m_timerRepository->update(timer);
    if (updateResult.isSuccess()) {
        Serial.printf("✅ TimerService: Timer reset - ID:%d, NextTrigger:%d\n", 
                     timerId, timer.nextTriggerTime);
        return true;
    } else {
        recordError("resetTimer", updateResult.getErrorMessage());
        return false;
    }
}

size_t TimerService::resetAllTimers() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_initialized) {
        return 0;
    }
    
    auto timers = m_timerRepository->getAll();
    size_t resetCount = 0;
    
    for (auto& timer : timers) {
        if (timer.enabled) {
            timer.nextTriggerTime = calculateNextTriggerTime(timer);
            auto updateResult = m_timerRepository->update(timer);
            if (updateResult.isSuccess()) {
                resetCount++;
            }
        }
    }
    
    Serial.printf("✅ TimerService: All timers reset - Count:%d\n", resetCount);
    
    return resetCount;
}

bool TimerService::startTriggerCheck() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) {
        return false;
    }

    if (m_triggerCheckRunning) {
        return true;
    }

    m_triggerCheckRunning = true;
    m_lastTriggerCheck = millis();

    Serial.println("✅ TimerService: Trigger check started");
    return true;
}

bool TimerService::stopTriggerCheck() {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_triggerCheckRunning = false;

    Serial.println("✅ TimerService: Trigger check stopped");
    return true;
}

std::vector<TimerData> TimerService::checkTriggeredTimers() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) {
        return {};
    }

    Timestamp currentTime = millis();
    return m_timerRepository->getTriggeredTimers(currentTime, m_triggerConfig.triggerTolerance);
}

TimerService::TriggerResult TimerService::processTimerTrigger(const TimerData& timer) {
    TriggerResult result;
    result.timerId = timer.id;
    result.triggerTime = millis();

    Timestamp startTime = millis();

    // 执行定时器关联的任务
    bool taskSuccess = executeTimerTask(timer);

    result.duration = millis() - startTime;
    result.success = taskSuccess;

    if (taskSuccess) {
        // 更新下次触发时间（如果是重复定时器）
        if (timer.isRepeating) {
            updateNextTriggerTime(timer);
        } else {
            // 一次性定时器，禁用它
            TimerData updatedTimer = timer;
            updatedTimer.enabled = false;
            updatedTimer.nextTriggerTime = 0;
            m_timerRepository->update(updatedTimer);
            pushStatusUpdate(timer.id, false);
        }

        m_totalTimersTriggered++;

        Serial.printf("✅ TimerService: Timer triggered successfully - ID:%d, Duration:%dms\n",
                     timer.id, result.duration);
    } else {
        result.errorMessage = "Failed to execute timer task";
        m_triggerFailures++;
        recordError("processTimerTrigger", result.errorMessage);
    }

    // 更新统计信息
    updateTriggerStatistics(timer.id, taskSuccess, result.duration);

    // 调用触发回调
    if (m_timerTriggeredCallback) {
        m_timerTriggeredCallback(result);
    }

    return result;
}

std::vector<TimerData> TimerService::findByTask(TaskID taskId) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) {
        return {};
    }

    return m_timerRepository->findByTaskId(taskId);
}

std::vector<TimerData> TimerService::getEnabledTimers() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) {
        return {};
    }

    return m_timerRepository->findWhere([](const TimerData& timer) {
        return timer.enabled;
    });
}

std::vector<TimerData> TimerService::getOneTimeTimers() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) {
        return {};
    }

    return m_timerRepository->findWhere([](const TimerData& timer) {
        return !timer.isRepeating;
    });
}

std::vector<TimerData> TimerService::findByWeekday(uint8_t weekday) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized || weekday > 6) {
        return {};
    }

    return m_timerRepository->findWhere([weekday](const TimerData& timer) {
        return timer.weekdays & (1 << weekday);
    });
}

std::vector<TimerData> TimerService::findByTimeRange(uint8_t startHour, uint8_t endHour) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized || startHour > 23 || endHour > 23) {
        return {};
    }

    return m_timerRepository->findWhere([startHour, endHour](const TimerData& timer) {
        return timer.hour >= startHour && timer.hour <= endHour;
    });
}

std::vector<TimerData> TimerService::getUpcomingTimers(uint32_t lookAheadMinutes) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) {
        return {};
    }

    Timestamp currentTime = millis();
    Timestamp lookAheadTime = currentTime + (lookAheadMinutes * 60 * 1000);

    return m_timerRepository->findWhere([currentTime, lookAheadTime](const TimerData& timer) {
        return timer.enabled && timer.nextTriggerTime > currentTime && timer.nextTriggerTime <= lookAheadTime;
    });
}

TimerService::BatchResult TimerService::createTimerBatch(const std::vector<TimerData>& timers) {
    std::lock_guard<std::mutex> lock(m_mutex);

    BatchResult result;
    result.totalCount = timers.size();

    if (!m_initialized) {
        result.errors.push_back("Service not initialized");
        result.failureCount = result.totalCount;
        return result;
    }

    Timestamp startTime = millis();

    // 验证所有定时器
    for (const auto& timer : timers) {
        if (!validateTimerData(timer)) {
            result.errors.push_back("Invalid timer data: " + timer.name);
            result.failureCount++;
        } else {
            auto timeValidation = validateTimerTime(timer);
            if (!timeValidation.isValid) {
                result.errors.push_back("Invalid timer time: " + timer.name + " - " + timeValidation.errorMessage);
                result.failureCount++;
                m_timeValidationFailures++;
            }
        }
    }

    // 如果有验证失败的定时器，停止操作
    if (result.failureCount > 0) {
        result.duration = millis() - startTime;
        return result;
    }

    // 批量创建
    m_timerRepository->beginTransaction();

    for (const auto& timer : timers) {
        auto createResult = m_timerRepository->create(timer);
        if (createResult.isSuccess()) {
            result.successCount++;
        } else {
            result.failureCount++;
            result.errors.push_back("Failed to create timer: " + timer.name);
        }
    }

    if (result.failureCount == 0) {
        m_timerRepository->commitTransaction();
    } else {
        m_timerRepository->rollbackTransaction();
        result.successCount = 0;
        result.failureCount = timers.size();
    }

    result.duration = millis() - startTime;
    m_totalBatchOperations++;

    Serial.printf("✅ TimerService: Batch create completed - Success:%d, Failed:%d\n",
                 result.successCount, result.failureCount);

    return result;
}

TimerService::BatchResult TimerService::deleteTimerBatch(const std::vector<TimerID>& timerIds) {
    std::lock_guard<std::mutex> lock(m_mutex);

    BatchResult result;
    result.totalCount = timerIds.size();

    if (!m_initialized) {
        result.errors.push_back("Service not initialized");
        result.failureCount = result.totalCount;
        return result;
    }

    Timestamp startTime = millis();

    // 批量删除
    for (TimerID id : timerIds) {
        bool success = m_timerRepository->deleteById(id);
        if (success) {
            result.successCount++;
            pushStatusUpdate(id, false);
        } else {
            result.failureCount++;
            result.errors.push_back("Failed to delete timer: " + String(id));
        }
    }

    result.duration = millis() - startTime;
    m_totalBatchOperations++;

    Serial.printf("✅ TimerService: Batch delete completed - Success:%d, Failed:%d\n",
                 result.successCount, result.failureCount);

    return result;
}

TimerService::BatchResult TimerService::setTimerEnabledBatch(const std::vector<TimerID>& timerIds, bool enabled) {
    std::lock_guard<std::mutex> lock(m_mutex);

    BatchResult result;
    result.totalCount = timerIds.size();

    if (!m_initialized) {
        result.errors.push_back("Service not initialized");
        result.failureCount = result.totalCount;
        return result;
    }

    Timestamp startTime = millis();

    // 批量启用/禁用
    for (TimerID id : timerIds) {
        bool success = enabled ? enableTimer(id) : disableTimer(id);
        if (success) {
            result.successCount++;
        } else {
            result.failureCount++;
            result.errors.push_back("Failed to " + String(enabled ? "enable" : "disable") + " timer: " + String(id));
        }
    }

    result.duration = millis() - startTime;
    m_totalBatchOperations++;

    Serial.printf("✅ TimerService: Batch %s completed - Success:%d, Failed:%d\n",
                 enabled ? "enable" : "disable", result.successCount, result.failureCount);

    return result;
}

TimerService::BatchResult TimerService::importTimers(const String& jsonData) {
    std::lock_guard<std::mutex> lock(m_mutex);

    BatchResult result;

    if (!m_initialized) {
        result.errors.push_back("Service not initialized");
        return result;
    }

    Timestamp startTime = millis();

    // 解析JSON数据
    DynamicJsonDocument doc(jsonData.length() + 1024);
    DeserializationError error = deserializeJson(doc, jsonData);

    if (error) {
        result.errors.push_back("Invalid JSON format: " + String(error.c_str()));
        return result;
    }

    // 检查是否是定时器数组
    if (!doc.is<JsonArray>()) {
        result.errors.push_back("JSON must be an array of timers");
        return result;
    }

    JsonArray timersArray = doc.as<JsonArray>();
    result.totalCount = timersArray.size();

    // 转换JSON为定时器数据
    std::vector<TimerData> timers;
    timers.reserve(timersArray.size());

    for (JsonVariant item : timersArray) {
        if (item.is<JsonObject>()) {
            auto timerResult = JSONConverter::timerFromJson(item.as<JsonObject>());
            if (timerResult.isSuccess()) {
                // 生成新的ID
                TimerData timer = timerResult.getValue();
                timer.id = GENERATE_TIMER_ID();
                timer.createdTime = millis();
                timer.modifiedTime = timer.createdTime;

                timers.push_back(timer);
            } else {
                result.errors.push_back("Invalid timer data in import");
                result.failureCount++;
            }
        } else {
            result.errors.push_back("Invalid timer format in import");
            result.failureCount++;
        }
    }

    // 批量创建定时器
    if (!timers.empty()) {
        auto batchResult = createTimerBatch(timers);
        result.successCount = batchResult.successCount;
        result.failureCount += batchResult.failureCount;

        for (const String& error : batchResult.errors) {
            result.errors.push_back(error);
        }
    }

    result.duration = millis() - startTime;
    m_totalBatchOperations++;

    Serial.printf("✅ TimerService: Import completed - Success:%d, Failed:%d\n",
                 result.successCount, result.failureCount);

    return result;
}

String TimerService::exportTimers(const std::vector<TimerID>& timerIds) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) {
        return "{}";
    }

    std::vector<TimerData> timers;

    if (timerIds.empty()) {
        // 导出所有定时器
        timers = m_timerRepository->getAll();
    } else {
        // 导出指定定时器
        timers.reserve(timerIds.size());
        for (TimerID id : timerIds) {
            auto result = m_timerRepository->getById(id);
            if (result.isSuccess()) {
                timers.push_back(result.getValue());
            }
        }
    }

    // 转换为JSON
    DynamicJsonDocument doc(timers.size() * 1024 + 1024);
    JsonArray timersArray = JSONConverter::timersToJsonArray(timers, doc);

    String jsonString;
    serializeJson(timersArray, jsonString);

    Serial.printf("✅ TimerService: Exported %d timers\n", timers.size());

    return jsonString;
}

TimerRepository::SystemTime TimerService::getCurrentTime() {
    return m_timerRepository->getCurrentSystemTime();
}

TimerRepository::TimeValidationResult TimerService::validateTimerTime(const TimerData& timer) {
    return m_timerRepository->validateTime(timer);
}

Timestamp TimerService::calculateNextTriggerTime(const TimerData& timer) {
    return m_timerRepository->calculateNextTriggerTime(timer);
}

bool TimerService::syncSystemTime() {
    if (!m_initialized) {
        return false;
    }

    // 这里可以实现NTP时间同步
    // 目前返回true表示同步成功
    Serial.println("✅ TimerService: System time synced");
    return true;
}

JsonObject TimerService::getStatistics(JsonDocument& doc) {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject stats = doc.createNestedObject("timer_service_statistics");

    stats["initialized"] = m_initialized;
    stats["trigger_check_running"] = m_triggerCheckRunning;
    stats["total_timers_triggered"] = m_totalTimersTriggered;
    stats["total_batch_operations"] = m_totalBatchOperations;
    stats["trigger_failures"] = m_triggerFailures;
    stats["time_validation_failures"] = m_timeValidationFailures;

    // 计算成功率
    if (m_totalTimersTriggered + m_triggerFailures > 0) {
        stats["trigger_success_rate"] = (float)m_totalTimersTriggered / (m_totalTimersTriggered + m_triggerFailures);
    }

    // 仓库统计
    if (m_timerRepository) {
        JsonObject repoStats = m_timerRepository->getDetailedStatistics(doc);
        stats["repository"] = repoStats;
    }

    return stats;
}

void TimerService::resetStatistics() {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_totalTimersTriggered = 0;
    m_totalBatchOperations = 0;
    m_triggerFailures = 0;
    m_timeValidationFailures = 0;

    Serial.println("✅ TimerService: Statistics reset");
}

JsonObject TimerService::getServiceStatus(JsonDocument& doc) {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject status = doc.createNestedObject("timer_service_status");

    status["initialized"] = m_initialized;
    status["trigger_check_running"] = m_triggerCheckRunning;
    status["last_trigger_check"] = m_lastTriggerCheck;
    status["last_cleanup_time"] = m_lastCleanupTime;

    // 配置信息
    JsonObject triggerConfig = status.createNestedObject("trigger_config");
    triggerConfig["check_interval"] = m_triggerConfig.checkInterval;
    triggerConfig["trigger_tolerance"] = m_triggerConfig.triggerTolerance;
    triggerConfig["enable_auto_cleanup"] = m_triggerConfig.enableAutoCleanup;
    triggerConfig["cleanup_interval"] = m_triggerConfig.cleanupInterval;
    triggerConfig["enable_time_sync"] = m_triggerConfig.enableTimeSync;

    JsonObject timeConfig = status.createNestedObject("time_config");
    timeConfig["timezone_offset"] = m_timeConfig.timezoneOffset;
    timeConfig["enable_dst"] = m_timeConfig.enableDST;
    timeConfig["enable_24hour_format"] = m_timeConfig.enable24HourFormat;
    timeConfig["ntp_server"] = m_timeConfig.ntpServer;

    return status;
}

JsonObject TimerService::getTriggerStatus(JsonDocument& doc) {
    std::lock_guard<std::mutex> lock(m_mutex);

    JsonObject status = doc.createNestedObject("trigger_status");

    status["trigger_check_running"] = m_triggerCheckRunning;
    status["last_check"] = m_lastTriggerCheck;
    status["check_interval"] = m_triggerConfig.checkInterval;

    // 获取即将触发的定时器
    auto upcomingTimers = getUpcomingTimers(60); // 未来1小时
    status["upcoming_timers_count"] = upcomingTimers.size();

    JsonArray upcomingArray = status.createNestedArray("upcoming_timers");
    for (const auto& timer : upcomingTimers) {
        JsonObject timerObj = upcomingArray.createNestedObject();
        timerObj["id"] = timer.id;
        timerObj["name"] = timer.name;
        timerObj["next_trigger"] = timer.nextTriggerTime;
    }

    return status;
}

void TimerService::updateTriggerConfig(const TriggerConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_triggerConfig = config;
    Serial.println("✅ TimerService: Trigger config updated");
}

void TimerService::updateTimeConfig(const TimeConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_timeConfig = config;
    Serial.println("✅ TimerService: Time config updated");
}

void TimerService::setTimerTriggeredCallback(std::function<void(const TriggerResult&)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_timerTriggeredCallback = callback;
}

void TimerService::setTimerStatusChangedCallback(std::function<void(TimerID, bool)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_timerStatusChangedCallback = callback;
}

void TimerService::setErrorCallback(std::function<void(const String&)> callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_errorCallback = callback;
}

void TimerService::loop() {
    if (!m_initialized) {
        return;
    }

    // 处理定时器触发检查
    if (m_triggerCheckRunning) {
        processTriggerCheck();
    }

    // 处理自动清理
    if (m_triggerConfig.enableAutoCleanup) {
        processAutoCleanup();
    }
}

// ==================== 内部实现方法 ====================

bool TimerService::validateTimerData(const TimerData& timer) {
    // 基础验证
    if (timer.name.isEmpty()) {
        return false;
    }

    // 时间验证
    if (timer.hour > 23 || timer.minute > 59 || timer.second > 59) {
        return false;
    }

    // 星期验证
    if (timer.weekdays > 0x7F) { // 0-6位表示周日到周六
        return false;
    }

    // 任务验证
    if (timer.taskId == INVALID_ID) {
        return false;
    }

    return true;
}

void TimerService::processTriggerCheck() {
    Timestamp currentTime = millis();

    // 检查触发间隔
    if (currentTime - m_lastTriggerCheck < m_triggerConfig.checkInterval) {
        return;
    }

    m_lastTriggerCheck = currentTime;

    // 获取需要触发的定时器
    auto triggeredTimers = checkTriggeredTimers();

    // 处理每个触发的定时器
    for (const auto& timer : triggeredTimers) {
        processTimerTrigger(timer);
    }
}

void TimerService::processAutoCleanup() {
    Timestamp currentTime = millis();

    // 检查清理间隔
    if (currentTime - m_lastCleanupTime < m_triggerConfig.cleanupInterval) {
        return;
    }

    m_lastCleanupTime = currentTime;

    // 清理已过期的一次性定时器
    auto expiredTimers = m_timerRepository->findWhere([currentTime](const TimerData& timer) {
        return !timer.isRepeating && !timer.enabled && timer.nextTriggerTime < currentTime;
    });

    for (const auto& timer : expiredTimers) {
        m_timerRepository->deleteById(timer.id);
    }

    if (!expiredTimers.empty()) {
        Serial.printf("✅ TimerService: Auto cleanup completed - Removed %d expired timers\n", expiredTimers.size());
    }
}

bool TimerService::executeTimerTask(const TimerData& timer) {
    if (!m_taskService) {
        return false;
    }

    // 执行定时器关联的任务
    auto executionResult = m_taskService->executeTask(timer.taskId);
    return executionResult.success;
}

bool TimerService::updateNextTriggerTime(const TimerData& timer) {
    TimerData updatedTimer = timer;
    updatedTimer.nextTriggerTime = calculateNextTriggerTime(timer);

    auto updateResult = m_timerRepository->update(updatedTimer);
    return updateResult.isSuccess();
}

void TimerService::pushStatusUpdate(TimerID timerId, bool enabled) {
    if (m_wsManager) {
        m_wsManager->pushTimerStatus(timerId, enabled);
    }

    // 调用状态变化回调
    if (m_timerStatusChangedCallback) {
        m_timerStatusChangedCallback(timerId, enabled);
    }
}

void TimerService::recordError(const String& operation, const String& error) {
    String fullError = "TimerService::" + operation + " - " + error;
    Serial.println("❌ " + fullError);

    // 推送错误通知
    if (m_wsManager) {
        m_wsManager->pushErrorNotification(fullError, ErrorLevel::ERROR);
    }

    // 调用错误回调
    if (m_errorCallback) {
        m_errorCallback(fullError);
    }
}

void TimerService::updateTriggerStatistics(TimerID timerId, bool success, uint32_t duration) {
    // 可以在这里添加更详细的统计信息记录
    // 比如记录每个定时器的触发历史、平均执行时间等
}
