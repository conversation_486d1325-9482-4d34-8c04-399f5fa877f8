#pragma once

#include "../core/DataStructures.h"
#include "../data/TimerRepository.h"
#include "../data/TaskRepository.h"
#include "../services/TaskService.h"
#include "../network/WSManager.h"
#include <functional>
#include <mutex>
#include <vector>

/**
 * ESP32-S3 红外控制系统 - 定时器业务服务
 * 
 * 功能：
 * 1. 定时器业务逻辑封装
 * 2. 定时器触发检查和管理
 * 3. 定时器状态监控和同步
 * 4. 批量定时器操作
 * 5. 时间计算和验证
 * 
 * 设计原则：
 * - 时间管理：精确的时间计算和触发
 * - 状态同步：实时定时器状态推送
 * - 批量操作：高效的批量定时器管理
 * - 错误恢复：定时器失败重试机制
 * - 性能优化：智能触发检查和缓存
 * 
 * 基于：ESP32-S3红外控制系统-完整后端开发标准文档.md
 */

class TimerService {
public:
    // 触发配置结构
    struct TriggerConfig {
        uint32_t checkInterval;        // 检查间隔（毫秒）
        uint32_t triggerTolerance;     // 触发容差（毫秒）
        bool enableAutoCleanup;        // 是否启用自动清理
        uint32_t cleanupInterval;      // 清理间隔（毫秒）
        bool enableTimeSync;           // 是否启用时间同步
        
        TriggerConfig() : checkInterval(1000), triggerTolerance(500),
                         enableAutoCleanup(true), cleanupInterval(24 * 60 * 60 * 1000),
                         enableTimeSync(true) {}
    };
    
    // 时间配置结构
    struct TimeConfig {
        int8_t timezoneOffset;         // 时区偏移（小时）
        bool enableDST;                // 是否启用夏令时
        bool enable24HourFormat;       // 是否启用24小时格式
        String ntpServer;              // NTP服务器
        
        TimeConfig() : timezoneOffset(8), enableDST(false),
                      enable24HourFormat(true), ntpServer("pool.ntp.org") {}
    };
    
    // 触发结果结构
    struct TriggerResult {
        TimerID timerId;               // 定时器ID
        bool success;                  // 是否成功
        String errorMessage;           // 错误消息
        Timestamp triggerTime;         // 触发时间
        uint32_t duration;             // 执行时间（毫秒）
        
        TriggerResult() : timerId(INVALID_ID), success(false), triggerTime(0), duration(0) {}
    };
    
    // 批量操作结果
    struct BatchResult {
        size_t totalCount;             // 总数量
        size_t successCount;           // 成功数量
        size_t failureCount;           // 失败数量
        std::vector<String> errors;    // 错误列表
        uint32_t duration;             // 执行时间（毫秒）
        
        BatchResult() : totalCount(0), successCount(0), failureCount(0), duration(0) {}
    };

private:
    // 依赖组件
    TimerRepository* m_timerRepository;
    TaskRepository* m_taskRepository;
    TaskService* m_taskService;
    WSManager* m_wsManager;
    
    // 配置
    TriggerConfig m_triggerConfig;
    TimeConfig m_timeConfig;
    
    // 状态
    bool m_initialized;
    bool m_triggerCheckRunning;
    Timestamp m_lastTriggerCheck;
    Timestamp m_lastCleanupTime;
    
    // 统计信息
    uint32_t m_totalTimersTriggered;
    uint32_t m_totalBatchOperations;
    uint32_t m_triggerFailures;
    uint32_t m_timeValidationFailures;
    
    // 线程安全
    mutable std::mutex m_mutex;
    
    // 回调函数
    std::function<void(const TriggerResult&)> m_timerTriggeredCallback;
    std::function<void(TimerID, bool)> m_timerStatusChangedCallback;
    std::function<void(const String&)> m_errorCallback;

public:
    /**
     * 构造函数
     * @param timerRepository 定时器仓库
     * @param taskRepository 任务仓库
     * @param taskService 任务服务
     * @param wsManager WebSocket管理器
     */
    TimerService(TimerRepository* timerRepository, TaskRepository* taskRepository,
                 TaskService* taskService, WSManager* wsManager);
    
    /**
     * 析构函数
     */
    ~TimerService();
    
    // ==================== 生命周期管理 ====================
    
    /**
     * 初始化定时器服务
     * @return 是否成功
     */
    bool initialize();
    
    /**
     * 清理资源
     */
    void cleanup();
    
    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return m_initialized; }
    
    // ==================== 定时器CRUD操作 ====================
    
    /**
     * 创建新定时器
     * @param timer 定时器数据
     * @return 操作结果
     */
    Result<TimerData> createTimer(const TimerData& timer);
    
    /**
     * 获取定时器
     * @param timerId 定时器ID
     * @return 操作结果
     */
    Result<TimerData> getTimer(TimerID timerId);
    
    /**
     * 更新定时器
     * @param timer 定时器数据
     * @return 操作结果
     */
    Result<TimerData> updateTimer(const TimerData& timer);
    
    /**
     * 删除定时器
     * @param timerId 定时器ID
     * @return 是否成功
     */
    bool deleteTimer(TimerID timerId);
    
    /**
     * 获取所有定时器
     * @return 定时器列表
     */
    std::vector<TimerData> getAllTimers();
    
    // ==================== 定时器控制 ====================
    
    /**
     * 启用定时器
     * @param timerId 定时器ID
     * @return 是否成功
     */
    bool enableTimer(TimerID timerId);
    
    /**
     * 禁用定时器
     * @param timerId 定时器ID
     * @return 是否成功
     */
    bool disableTimer(TimerID timerId);
    
    /**
     * 手动触发定时器
     * @param timerId 定时器ID
     * @return 触发结果
     */
    TriggerResult triggerTimer(TimerID timerId);
    
    /**
     * 重置定时器（重新计算下次触发时间）
     * @param timerId 定时器ID
     * @return 是否成功
     */
    bool resetTimer(TimerID timerId);
    
    /**
     * 批量重置所有定时器
     * @return 重置的定时器数量
     */
    size_t resetAllTimers();
    
    // ==================== 定时器触发检查 ====================
    
    /**
     * 启动定时器触发检查
     * @return 是否成功
     */
    bool startTriggerCheck();
    
    /**
     * 停止定时器触发检查
     * @return 是否成功
     */
    bool stopTriggerCheck();
    
    /**
     * 检查是否有定时器需要触发
     * @return 需要触发的定时器列表
     */
    std::vector<TimerData> checkTriggeredTimers();
    
    /**
     * 处理定时器触发
     * @param timer 定时器数据
     * @return 触发结果
     */
    TriggerResult processTimerTrigger(const TimerData& timer);
    
    // ==================== 高级查询 ====================
    
    /**
     * 根据关联任务查找定时器
     * @param taskId 任务ID
     * @return 定时器列表
     */
    std::vector<TimerData> findByTask(TaskID taskId);
    
    /**
     * 获取启用的定时器
     * @return 定时器列表
     */
    std::vector<TimerData> getEnabledTimers();
    
    /**
     * 获取一次性定时器
     * @return 定时器列表
     */
    std::vector<TimerData> getOneTimeTimers();
    
    /**
     * 根据星期几查找定时器
     * @param weekday 星期几 (0=周日, 1=周一, ..., 6=周六)
     * @return 定时器列表
     */
    std::vector<TimerData> findByWeekday(uint8_t weekday);
    
    /**
     * 根据时间范围查找定时器
     * @param startHour 开始小时
     * @param endHour 结束小时
     * @return 定时器列表
     */
    std::vector<TimerData> findByTimeRange(uint8_t startHour, uint8_t endHour);
    
    /**
     * 获取即将触发的定时器
     * @param lookAheadMinutes 提前查看的分钟数
     * @return 定时器列表
     */
    std::vector<TimerData> getUpcomingTimers(uint32_t lookAheadMinutes = 60);
    
    // ==================== 批量操作 ====================
    
    /**
     * 批量创建定时器
     * @param timers 定时器列表
     * @return 批量操作结果
     */
    BatchResult createTimerBatch(const std::vector<TimerData>& timers);
    
    /**
     * 批量删除定时器
     * @param timerIds 定时器ID列表
     * @return 批量操作结果
     */
    BatchResult deleteTimerBatch(const std::vector<TimerID>& timerIds);
    
    /**
     * 批量启用/禁用定时器
     * @param timerIds 定时器ID列表
     * @param enabled 是否启用
     * @return 批量操作结果
     */
    BatchResult setTimerEnabledBatch(const std::vector<TimerID>& timerIds, bool enabled);
    
    /**
     * 导入定时器数据
     * @param jsonData JSON格式的定时器数据
     * @return 批量操作结果
     */
    BatchResult importTimers(const String& jsonData);
    
    /**
     * 导出定时器数据
     * @param timerIds 要导出的定时器ID列表（空表示全部）
     * @return JSON格式的定时器数据
     */
    String exportTimers(const std::vector<TimerID>& timerIds = {});
    
    // ==================== 时间管理 ====================
    
    /**
     * 获取当前系统时间
     * @return 系统时间结构
     */
    TimerRepository::SystemTime getCurrentTime();
    
    /**
     * 验证定时器时间设置
     * @param timer 定时器数据
     * @return 验证结果
     */
    TimerRepository::TimeValidationResult validateTimerTime(const TimerData& timer);
    
    /**
     * 计算下次触发时间
     * @param timer 定时器数据
     * @return 下次触发时间
     */
    Timestamp calculateNextTriggerTime(const TimerData& timer);
    
    /**
     * 同步系统时间
     * @return 是否成功
     */
    bool syncSystemTime();

    // ==================== 统计和监控 ====================

    /**
     * 获取定时器统计信息
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getStatistics(JsonDocument& doc);

    /**
     * 重置统计信息
     */
    void resetStatistics();

    /**
     * 获取服务状态
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getServiceStatus(JsonDocument& doc);

    /**
     * 获取触发状态
     * @param doc JSON文档
     * @return JSON对象
     */
    JsonObject getTriggerStatus(JsonDocument& doc);

    // ==================== 配置管理 ====================

    /**
     * 更新触发配置
     * @param config 触发配置
     */
    void updateTriggerConfig(const TriggerConfig& config);

    /**
     * 更新时间配置
     * @param config 时间配置
     */
    void updateTimeConfig(const TimeConfig& config);

    /**
     * 获取触发配置
     * @return 触发配置
     */
    const TriggerConfig& getTriggerConfig() const { return m_triggerConfig; }

    /**
     * 获取时间配置
     * @return 时间配置
     */
    const TimeConfig& getTimeConfig() const { return m_timeConfig; }

    // ==================== 回调设置 ====================

    /**
     * 设置定时器触发回调
     * @param callback 回调函数
     */
    void setTimerTriggeredCallback(std::function<void(const TriggerResult&)> callback);

    /**
     * 设置定时器状态变化回调
     * @param callback 回调函数
     */
    void setTimerStatusChangedCallback(std::function<void(TimerID, bool)> callback);

    /**
     * 设置错误回调
     * @param callback 回调函数
     */
    void setErrorCallback(std::function<void(const String&)> callback);

    // ==================== 循环处理 ====================

    /**
     * 主循环处理函数（需要在主循环中调用）
     */
    void loop();

private:
    // ==================== 内部实现方法 ====================

    /**
     * 验证定时器数据
     * @param timer 定时器数据
     * @return 验证结果
     */
    bool validateTimerData(const TimerData& timer);

    /**
     * 处理定时器触发检查
     */
    void processTriggerCheck();

    /**
     * 处理自动清理
     */
    void processAutoCleanup();

    /**
     * 执行定时器关联的任务
     * @param timer 定时器数据
     * @return 是否成功
     */
    bool executeTimerTask(const TimerData& timer);

    /**
     * 更新定时器下次触发时间
     * @param timer 定时器数据
     * @return 是否成功
     */
    bool updateNextTriggerTime(const TimerData& timer);

    /**
     * 推送状态更新
     * @param timerId 定时器ID
     * @param enabled 是否启用
     */
    void pushStatusUpdate(TimerID timerId, bool enabled);

    /**
     * 记录错误
     * @param operation 操作名称
     * @param error 错误信息
     */
    void recordError(const String& operation, const String& error);

    /**
     * 更新触发统计
     * @param timerId 定时器ID
     * @param success 是否成功
     * @param duration 执行时间
     */
    void updateTriggerStatistics(TimerID timerId, bool success, uint32_t duration);
};
